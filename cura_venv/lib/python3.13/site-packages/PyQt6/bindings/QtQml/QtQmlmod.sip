// QtQmlmod.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt6.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt6.QtQml, keyword_arguments="Optional", use_limited_api=True)

%Import QtCore/QtCoremod.sip
%Import QtNetwork/QtNetworkmod.sip

%Copying
Copyright (c) 2025 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt6.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%DefaultSupertype PyQt6.sip.simplewrapper

%Include qqml.sip
%Include qjsengine.sip
%Include qjsmanagedvalue.sip
%Include qjsprimitivevalue.sip
%Include qjsvalue.sip
%Include qjsvalueiterator.sip
%Include qqmlabstracturlinterceptor.sip
%Include qqmlapplicationengine.sip
%Include qqmlcomponent.sip
%Include qqmlcontext.sip
%Include qqmlengine.sip
%Include qqmlerror.sip
%Include qqmlexpression.sip
%Include qqmlextensionplugin.sip
%Include qqmlfileselector.sip
%Include qqmlincubator.sip
%Include qqmllist.sip
%Include qqmlnetworkaccessmanagerfactory.sip
%Include qqmlparserstatus.sip
%Include qqmlproperty.sip
%Include qqmlpropertymap.sip
%Include qqmlpropertyvaluesource.sip
%Include qqmlscriptstring.sip
%Include qmlattachedpropertiesobject.sip
%Include qpyqmllistproperty.sip
%Include qmlregistertype.sip
