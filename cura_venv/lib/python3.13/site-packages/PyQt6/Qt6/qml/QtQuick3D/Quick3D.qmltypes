import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/qquick3dabstractlight_p.h"
        name: "QQuick3DAbstractLight"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: [
            "QtQuick3D/Light 6.0",
            "QtQuick3D/Light 6.8",
            "QtQuick3D/Light 6.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1544, 1545]
        Enum {
            name: "QSSGShadowMapQuality"
            isScoped: true
            values: [
                "ShadowMapQualityLow",
                "ShadowMapQualityMedium",
                "ShadowMapQualityHigh",
                "ShadowMapQualityVeryHigh",
                "ShadowMapQualityUltra"
            ]
        }
        Enum {
            name: "QSSGSoftShadowQuality"
            isScoped: true
            values: ["Hard", "PCF4", "PCF8", "PCF16", "PCF32", "PCF64"]
        }
        Enum {
            name: "QSSGBakeMode"
            isScoped: true
            values: ["BakeModeDisabled", "BakeModeIndirect", "BakeModeAll"]
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
        }
        Property {
            name: "ambientColor"
            type: "QColor"
            read: "ambientColor"
            write: "setAmbientColor"
            notify: "ambientColorChanged"
            index: 1
        }
        Property {
            name: "brightness"
            type: "float"
            read: "brightness"
            write: "setBrightness"
            notify: "brightnessChanged"
            index: 2
        }
        Property {
            name: "scope"
            type: "QQuick3DNode"
            isPointer: true
            read: "scope"
            write: "setScope"
            notify: "scopeChanged"
            index: 3
        }
        Property {
            name: "castsShadow"
            type: "bool"
            read: "castsShadow"
            write: "setCastsShadow"
            notify: "castsShadowChanged"
            index: 4
        }
        Property {
            name: "shadowBias"
            type: "float"
            read: "shadowBias"
            write: "setShadowBias"
            notify: "shadowBiasChanged"
            index: 5
        }
        Property {
            name: "shadowFactor"
            type: "float"
            read: "shadowFactor"
            write: "setShadowFactor"
            notify: "shadowFactorChanged"
            index: 6
        }
        Property {
            name: "shadowMapQuality"
            type: "QSSGShadowMapQuality"
            read: "shadowMapQuality"
            write: "setShadowMapQuality"
            notify: "shadowMapQualityChanged"
            index: 7
        }
        Property {
            name: "shadowMapFar"
            type: "float"
            read: "shadowMapFar"
            write: "setShadowMapFar"
            notify: "shadowMapFarChanged"
            index: 8
        }
        Property {
            name: "shadowFilter"
            type: "float"
            read: "shadowFilter"
            write: "setShadowFilter"
            notify: "shadowFilterChanged"
            index: 9
        }
        Property {
            name: "bakeMode"
            type: "QSSGBakeMode"
            read: "bakeMode"
            write: "setBakeMode"
            notify: "bakeModeChanged"
            index: 10
        }
        Property {
            name: "softShadowQuality"
            revision: 1544
            type: "QSSGSoftShadowQuality"
            read: "softShadowQuality"
            write: "setSoftShadowQuality"
            notify: "softShadowQualityChanged"
            index: 11
            isFinal: true
        }
        Property {
            name: "pcfFactor"
            revision: 1544
            type: "float"
            read: "pcfFactor"
            write: "setPcfFactor"
            notify: "pcfFactorChanged"
            index: 12
            isFinal: true
        }
        Property {
            name: "use32BitShadowmap"
            revision: 1545
            type: "bool"
            read: "use32BitShadowmap"
            write: "setUse32BitShadowmap"
            notify: "use32BitShadowmapChanged"
            index: 13
            isFinal: true
        }
        Signal { name: "colorChanged" }
        Signal { name: "ambientColorChanged" }
        Signal { name: "brightnessChanged" }
        Signal { name: "scopeChanged" }
        Signal { name: "castsShadowChanged" }
        Signal { name: "shadowBiasChanged" }
        Signal { name: "shadowFactorChanged" }
        Signal { name: "shadowMapQualityChanged" }
        Signal { name: "shadowMapFarChanged" }
        Signal { name: "shadowFilterChanged" }
        Signal { name: "bakeModeChanged" }
        Signal { name: "softShadowQualityChanged"; revision: 1544 }
        Signal { name: "pcfFactorChanged"; revision: 1544 }
        Signal { name: "use32BitShadowmapChanged"; revision: 1545 }
        Method {
            name: "setColor"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setAmbientColor"
            Parameter { name: "ambientColor"; type: "QColor" }
        }
        Method {
            name: "setBrightness"
            Parameter { name: "brightness"; type: "float" }
        }
        Method {
            name: "setScope"
            Parameter { name: "scope"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setCastsShadow"
            Parameter { name: "castsShadow"; type: "bool" }
        }
        Method {
            name: "setShadowBias"
            Parameter { name: "shadowBias"; type: "float" }
        }
        Method {
            name: "setShadowFactor"
            Parameter { name: "shadowFactor"; type: "float" }
        }
        Method {
            name: "setShadowMapQuality"
            Parameter { name: "shadowMapQuality"; type: "QQuick3DAbstractLight::QSSGShadowMapQuality" }
        }
        Method {
            name: "setShadowMapFar"
            Parameter { name: "shadowMapFar"; type: "float" }
        }
        Method {
            name: "setShadowFilter"
            Parameter { name: "shadowFilter"; type: "float" }
        }
        Method {
            name: "setBakeMode"
            Parameter { name: "bakeMode"; type: "QQuick3DAbstractLight::QSSGBakeMode" }
        }
        Method {
            name: "setSoftShadowQuality"
            revision: 1544
            Parameter { name: "softShadowQuality"; type: "QQuick3DAbstractLight::QSSGSoftShadowQuality" }
        }
        Method {
            name: "setPcfFactor"
            revision: 1544
            Parameter { name: "pcfFactor"; type: "float" }
        }
        Method {
            name: "setUse32BitShadowmap"
            revision: 1545
            Parameter { name: "use32BitShadowmap"; type: "bool" }
        }
    }
    Component {
        file: "private/qquick3dbakedlightmap_p.h"
        name: "QQuick3DBakedLightmap"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D/BakedLightmap 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
        }
        Property {
            name: "key"
            type: "QString"
            read: "key"
            write: "setKey"
            notify: "keyChanged"
            index: 1
        }
        Property {
            name: "loadPrefix"
            type: "QString"
            read: "loadPrefix"
            write: "setLoadPrefix"
            notify: "loadPrefixChanged"
            index: 2
        }
        Signal { name: "changed" }
        Signal { name: "enabledChanged" }
        Signal { name: "keyChanged" }
        Signal { name: "loadPrefixChanged" }
        Method {
            name: "setEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setKey"
            Parameter { name: "key"; type: "QString" }
        }
        Method {
            name: "setLoadPrefix"
            Parameter { name: "loadPrefix"; type: "QString" }
        }
    }
    Component {
        file: "private/qquick3dmodel_p.h"
        name: "QQuick3DBounds3"
        accessSemantics: "value"
        exports: ["QtQuick3D/bounds 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "minimum"
            type: "QVector3D"
            read: "minimum"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "maximum"
            type: "QVector3D"
            read: "maximum"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquick3dcamera_p.h"
        name: "QQuick3DCamera"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: [
            "QtQuick3D/Camera 6.0",
            "QtQuick3D/Camera 6.4",
            "QtQuick3D/Camera 6.5"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1540, 1541]
        Property {
            name: "frustumCullingEnabled"
            type: "bool"
            read: "frustumCullingEnabled"
            write: "setFrustumCullingEnabled"
            notify: "frustumCullingEnabledChanged"
            index: 0
        }
        Property {
            name: "lookAtNode"
            revision: 1540
            type: "QQuick3DNode"
            isPointer: true
            read: "lookAtNode"
            write: "setLookAtNode"
            notify: "lookAtNodeChanged"
            index: 1
        }
        Property {
            name: "levelOfDetailBias"
            revision: 1541
            type: "float"
            read: "levelOfDetailBias"
            write: "setLevelOfDetailBias"
            notify: "levelOfDetailBiasChanged"
            index: 2
        }
        Signal { name: "frustumCullingEnabledChanged" }
        Signal { name: "lookAtNodeChanged"; revision: 1540 }
        Signal { name: "levelOfDetailBiasChanged"; revision: 1541 }
        Method {
            name: "setFrustumCullingEnabled"
            Parameter { name: "frustumCullingEnabled"; type: "bool" }
        }
        Method {
            name: "setLookAtNode"
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setLevelOfDetailBias"
            revision: 1541
            Parameter { name: "newLevelOFDetailBias"; type: "float" }
        }
        Method { name: "updateLookAt" }
        Method {
            name: "mapToViewport"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "scenePos"; type: "QVector3D" }
        }
        Method {
            name: "mapFromViewport"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "viewportPos"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            Parameter { name: "scenePos"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true }
        }
    }
    Component {
        file: "private/qquick3dcubemaptexture_p.h"
        name: "QQuick3DCubeMapTexture"
        accessSemantics: "reference"
        prototype: "QQuick3DTexture"
        exports: [
            "QtQuick3D/CubeMapTexture 6.0",
            "QtQuick3D/CubeMapTexture 6.2",
            "QtQuick3D/CubeMapTexture 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1538, 1543]
    }
    Component {
        file: "private/qquick3dcustomcamera_p.h"
        name: "QQuick3DCustomCamera"
        accessSemantics: "reference"
        prototype: "QQuick3DCamera"
        exports: [
            "QtQuick3D/CustomCamera 6.0",
            "QtQuick3D/CustomCamera 6.4",
            "QtQuick3D/CustomCamera 6.5"
        ]
        exportMetaObjectRevisions: [1536, 1540, 1541]
        Property {
            name: "projection"
            type: "QMatrix4x4"
            read: "projection"
            write: "setProjection"
            notify: "projectionChanged"
            index: 0
        }
        Signal { name: "projectionChanged" }
        Method {
            name: "setProjection"
            Parameter { name: "projection"; type: "QMatrix4x4" }
        }
    }
    Component {
        file: "private/qquick3dcustommaterial_p.h"
        name: "QQuick3DCustomMaterial"
        accessSemantics: "reference"
        prototype: "QQuick3DMaterial"
        exports: [
            "QtQuick3D/CustomMaterial 6.0",
            "QtQuick3D/CustomMaterial 6.7",
            "QtQuick3D/CustomMaterial 6.8"
        ]
        exportMetaObjectRevisions: [1536, 1543, 1544]
        Enum {
            name: "ShadingMode"
            isScoped: true
            values: ["Unshaded", "Shaded"]
        }
        Enum {
            name: "BlendMode"
            isScoped: true
            values: [
                "NoBlend",
                "Zero",
                "One",
                "SrcColor",
                "OneMinusSrcColor",
                "DstColor",
                "OneMinusDstColor",
                "SrcAlpha",
                "OneMinusSrcAlpha",
                "DstAlpha",
                "OneMinusDstAlpha",
                "ConstantColor",
                "OneMinusConstantColor",
                "ConstantAlpha",
                "OneMinusConstantAlpha",
                "SrcAlphaSaturate"
            ]
        }
        Property {
            name: "shadingMode"
            type: "ShadingMode"
            read: "shadingMode"
            write: "setShadingMode"
            notify: "shadingModeChanged"
            index: 0
        }
        Property {
            name: "fragmentShader"
            type: "QUrl"
            read: "fragmentShader"
            write: "setFragmentShader"
            notify: "fragmentShaderChanged"
            index: 1
        }
        Property {
            name: "vertexShader"
            type: "QUrl"
            read: "vertexShader"
            write: "setVertexShader"
            notify: "vertexShaderChanged"
            index: 2
        }
        Property {
            name: "__fragmentShaderCode"
            revision: 1544
            type: "QString"
            read: "fragmentShaderCode"
            write: "setFragmentShaderCode"
            notify: "fragmentShaderCodeChanged"
            index: 3
        }
        Property {
            name: "__vertexShaderCode"
            revision: 1544
            type: "QString"
            read: "vertexShaderCode"
            write: "setVertexShaderCode"
            notify: "vertexShaderCodeChanged"
            index: 4
        }
        Property {
            name: "sourceBlend"
            type: "BlendMode"
            read: "srcBlend"
            write: "setSrcBlend"
            notify: "srcBlendChanged"
            index: 5
        }
        Property {
            name: "destinationBlend"
            type: "BlendMode"
            read: "dstBlend"
            write: "setDstBlend"
            notify: "dstBlendChanged"
            index: 6
        }
        Property {
            name: "sourceAlphaBlend"
            revision: 1543
            type: "BlendMode"
            read: "srcAlphaBlend"
            write: "setSrcAlphaBlend"
            notify: "srcAlphaBlendChanged"
            index: 7
        }
        Property {
            name: "destinationAlphaBlend"
            revision: 1543
            type: "BlendMode"
            read: "dstAlphaBlend"
            write: "setDstAlphaBlend"
            notify: "dstAlphaBlendChanged"
            index: 8
        }
        Property {
            name: "alwaysDirty"
            type: "bool"
            read: "alwaysDirty"
            write: "setAlwaysDirty"
            notify: "alwaysDirtyChanged"
            index: 9
        }
        Property {
            name: "lineWidth"
            type: "float"
            read: "lineWidth"
            write: "setLineWidth"
            notify: "lineWidthChanged"
            index: 10
        }
        Signal { name: "shadingModeChanged" }
        Signal { name: "vertexShaderChanged" }
        Signal { name: "fragmentShaderChanged" }
        Signal { name: "vertexShaderCodeChanged"; revision: 1544 }
        Signal { name: "fragmentShaderCodeChanged"; revision: 1544 }
        Signal { name: "srcBlendChanged" }
        Signal { name: "dstBlendChanged" }
        Signal { name: "srcAlphaBlendChanged"; revision: 1543 }
        Signal { name: "dstAlphaBlendChanged"; revision: 1543 }
        Signal { name: "alwaysDirtyChanged" }
        Signal { name: "lineWidthChanged" }
        Method {
            name: "setShadingMode"
            Parameter { name: "mode"; type: "QQuick3DCustomMaterial::ShadingMode" }
        }
        Method {
            name: "setVertexShader"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "setFragmentShader"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "setVertexShaderCode"
            revision: 1544
            Parameter { name: "code"; type: "QString" }
        }
        Method {
            name: "setFragmentShaderCode"
            revision: 1544
            Parameter { name: "code"; type: "QString" }
        }
        Method {
            name: "setSrcBlend"
            Parameter { name: "mode"; type: "QQuick3DCustomMaterial::BlendMode" }
        }
        Method {
            name: "setDstBlend"
            Parameter { name: "mode"; type: "QQuick3DCustomMaterial::BlendMode" }
        }
        Method {
            name: "setSrcAlphaBlend"
            revision: 1543
            Parameter { name: "mode"; type: "QQuick3DCustomMaterial::BlendMode" }
        }
        Method {
            name: "setDstAlphaBlend"
            revision: 1543
            Parameter { name: "mode"; type: "QQuick3DCustomMaterial::BlendMode" }
        }
        Method {
            name: "setAlwaysDirty"
            Parameter { name: "alwaysDirty"; type: "bool" }
        }
        Method {
            name: "setLineWidth"
            Parameter { name: "width"; type: "float" }
        }
        Method { name: "onPropertyDirty" }
        Method { name: "onTextureDirty" }
    }
    Component {
        file: "private/qquick3ddebugsettings_p.h"
        name: "QQuick3DDebugSettings"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtQuick3D/DebugSettings 6.0",
            "QtQuick3D/DebugSettings 6.8",
            "QtQuick3D/DebugSettings 6.9"
        ]
        exportMetaObjectRevisions: [1536, 1544, 1545]
        Enum {
            name: "QQuick3DMaterialOverrides"
            values: [
                "None",
                "BaseColor",
                "Roughness",
                "Metalness",
                "Diffuse",
                "Specular",
                "ShadowOcclusion",
                "Emission",
                "AmbientOcclusion",
                "Normals",
                "Tangents",
                "Binormals",
                "F0"
            ]
        }
        Property {
            name: "materialOverride"
            type: "QQuick3DMaterialOverrides"
            read: "materialOverride"
            write: "setMaterialOverride"
            notify: "materialOverrideChanged"
            index: 0
        }
        Property {
            name: "wireframeEnabled"
            type: "bool"
            read: "wireframeEnabled"
            write: "setWireframeEnabled"
            notify: "wireframeEnabledChanged"
            index: 1
        }
        Property {
            name: "drawDirectionalLightShadowBoxes"
            revision: 1544
            type: "bool"
            read: "drawDirectionalLightShadowBoxes"
            write: "setDrawDirectionalLightShadowBoxes"
            notify: "drawDirectionalLightShadowBoxesChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "drawPointLightShadowBoxes"
            revision: 1545
            type: "bool"
            read: "drawPointLightShadowBoxes"
            write: "setDrawPointLightShadowBoxes"
            notify: "drawPointLightShadowBoxesChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "drawShadowCastingBounds"
            revision: 1544
            type: "bool"
            read: "drawShadowCastingBounds"
            write: "setDrawShadowCastingBounds"
            notify: "drawShadowCastingBoundsChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "drawShadowReceivingBounds"
            revision: 1544
            type: "bool"
            read: "drawShadowReceivingBounds"
            write: "setDrawShadowReceivingBounds"
            notify: "drawShadowReceivingBoundsChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "drawCascades"
            revision: 1544
            type: "bool"
            read: "drawCascades"
            write: "setDrawCascades"
            notify: "drawCascadesChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "drawSceneCascadeIntersection"
            revision: 1544
            type: "bool"
            read: "drawSceneCascadeIntersection"
            write: "setDrawSceneCascadeIntersection"
            notify: "drawSceneCascadeIntersectionChanged"
            index: 7
            isFinal: true
        }
        Property {
            name: "disableShadowCameraUpdate"
            revision: 1544
            type: "bool"
            read: "disableShadowCameraUpdate"
            write: "setDisableShadowCameraUpdate"
            notify: "disableShadowCameraUpdateChanged"
            index: 8
            isFinal: true
        }
        Signal { name: "materialOverrideChanged" }
        Signal { name: "wireframeEnabledChanged" }
        Signal { name: "drawDirectionalLightShadowBoxesChanged"; revision: 1544 }
        Signal { name: "drawPointLightShadowBoxesChanged"; revision: 1545 }
        Signal { name: "drawShadowCastingBoundsChanged"; revision: 1544 }
        Signal { name: "drawShadowReceivingBoundsChanged"; revision: 1544 }
        Signal { name: "drawCascadesChanged"; revision: 1544 }
        Signal { name: "drawSceneCascadeIntersectionChanged"; revision: 1544 }
        Signal { name: "disableShadowCameraUpdateChanged"; revision: 1544 }
        Signal { name: "changed" }
    }
    Component {
        file: "private/qquick3ddefaultmaterial_p.h"
        name: "QQuick3DDefaultMaterial"
        accessSemantics: "reference"
        prototype: "QQuick3DMaterial"
        exports: ["QtQuick3D/DefaultMaterial 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Lighting"
            values: ["NoLighting", "FragmentLighting"]
        }
        Enum {
            name: "BlendMode"
            values: ["SourceOver", "Screen", "Multiply"]
        }
        Enum {
            name: "SpecularModel"
            values: ["Default", "KGGX"]
        }
        Property {
            name: "lighting"
            type: "Lighting"
            read: "lighting"
            write: "setLighting"
            notify: "lightingChanged"
            index: 0
        }
        Property {
            name: "blendMode"
            type: "BlendMode"
            read: "blendMode"
            write: "setBlendMode"
            notify: "blendModeChanged"
            index: 1
        }
        Property {
            name: "diffuseColor"
            type: "QColor"
            read: "diffuseColor"
            write: "setDiffuseColor"
            notify: "diffuseColorChanged"
            index: 2
        }
        Property {
            name: "diffuseMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "diffuseMap"
            write: "setDiffuseMap"
            notify: "diffuseMapChanged"
            index: 3
        }
        Property {
            name: "emissiveFactor"
            type: "QVector3D"
            read: "emissiveFactor"
            write: "setEmissiveFactor"
            notify: "emissiveFactorChanged"
            index: 4
        }
        Property {
            name: "emissiveMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "emissiveMap"
            write: "setEmissiveMap"
            notify: "emissiveMapChanged"
            index: 5
        }
        Property {
            name: "specularReflectionMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "specularReflectionMap"
            write: "setSpecularReflectionMap"
            notify: "specularReflectionMapChanged"
            index: 6
        }
        Property {
            name: "specularMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "specularMap"
            write: "setSpecularMap"
            notify: "specularMapChanged"
            index: 7
        }
        Property {
            name: "specularModel"
            type: "SpecularModel"
            read: "specularModel"
            write: "setSpecularModel"
            notify: "specularModelChanged"
            index: 8
        }
        Property {
            name: "specularTint"
            type: "QColor"
            read: "specularTint"
            write: "setSpecularTint"
            notify: "specularTintChanged"
            index: 9
        }
        Property {
            name: "indexOfRefraction"
            type: "float"
            read: "indexOfRefraction"
            write: "setIndexOfRefraction"
            notify: "indexOfRefractionChanged"
            index: 10
        }
        Property {
            name: "fresnelPower"
            type: "float"
            read: "fresnelPower"
            write: "setFresnelPower"
            notify: "fresnelPowerChanged"
            index: 11
        }
        Property {
            name: "specularAmount"
            type: "float"
            read: "specularAmount"
            write: "setSpecularAmount"
            notify: "specularAmountChanged"
            index: 12
        }
        Property {
            name: "specularRoughness"
            type: "float"
            read: "specularRoughness"
            write: "setSpecularRoughness"
            notify: "specularRoughnessChanged"
            index: 13
        }
        Property {
            name: "roughnessMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "roughnessMap"
            write: "setRoughnessMap"
            notify: "roughnessMapChanged"
            index: 14
        }
        Property {
            name: "roughnessChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "roughnessChannel"
            write: "setRoughnessChannel"
            notify: "roughnessChannelChanged"
            index: 15
        }
        Property {
            name: "opacity"
            type: "float"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 16
        }
        Property {
            name: "opacityMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "opacityMap"
            write: "setOpacityMap"
            notify: "opacityMapChanged"
            index: 17
        }
        Property {
            name: "opacityChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "opacityChannel"
            write: "setOpacityChannel"
            notify: "opacityChannelChanged"
            index: 18
        }
        Property {
            name: "bumpMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "bumpMap"
            write: "setBumpMap"
            notify: "bumpMapChanged"
            index: 19
        }
        Property {
            name: "bumpAmount"
            type: "float"
            read: "bumpAmount"
            write: "setBumpAmount"
            notify: "bumpAmountChanged"
            index: 20
        }
        Property {
            name: "normalMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "normalMap"
            write: "setNormalMap"
            notify: "normalMapChanged"
            index: 21
        }
        Property {
            name: "translucencyMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "translucencyMap"
            write: "setTranslucencyMap"
            notify: "translucencyMapChanged"
            index: 22
        }
        Property {
            name: "translucencyChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "translucencyChannel"
            write: "setTranslucencyChannel"
            notify: "translucencyChannelChanged"
            index: 23
        }
        Property {
            name: "translucentFalloff"
            type: "float"
            read: "translucentFalloff"
            write: "setTranslucentFalloff"
            notify: "translucentFalloffChanged"
            index: 24
        }
        Property {
            name: "diffuseLightWrap"
            type: "float"
            read: "diffuseLightWrap"
            write: "setDiffuseLightWrap"
            notify: "diffuseLightWrapChanged"
            index: 25
        }
        Property {
            name: "vertexColorsEnabled"
            type: "bool"
            read: "vertexColorsEnabled"
            write: "setVertexColorsEnabled"
            notify: "vertexColorsEnabledChanged"
            index: 26
        }
        Property {
            name: "pointSize"
            type: "float"
            read: "pointSize"
            write: "setPointSize"
            notify: "pointSizeChanged"
            index: 27
        }
        Property {
            name: "lineWidth"
            type: "float"
            read: "lineWidth"
            write: "setLineWidth"
            notify: "lineWidthChanged"
            index: 28
        }
        Signal {
            name: "lightingChanged"
            Parameter { name: "lighting"; type: "QQuick3DDefaultMaterial::Lighting" }
        }
        Signal {
            name: "blendModeChanged"
            Parameter { name: "blendMode"; type: "QQuick3DDefaultMaterial::BlendMode" }
        }
        Signal {
            name: "diffuseColorChanged"
            Parameter { name: "diffuseColor"; type: "QColor" }
        }
        Signal {
            name: "diffuseMapChanged"
            Parameter { name: "diffuseMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "emissiveFactorChanged"
            Parameter { name: "emissiveFactor"; type: "QVector3D" }
        }
        Signal {
            name: "emissiveMapChanged"
            Parameter { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "specularReflectionMapChanged"
            Parameter { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "specularMapChanged"
            Parameter { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "specularModelChanged"
            Parameter { name: "specularModel"; type: "QQuick3DDefaultMaterial::SpecularModel" }
        }
        Signal {
            name: "specularTintChanged"
            Parameter { name: "specularTint"; type: "QColor" }
        }
        Signal {
            name: "indexOfRefractionChanged"
            Parameter { name: "indexOfRefraction"; type: "float" }
        }
        Signal {
            name: "fresnelPowerChanged"
            Parameter { name: "fresnelPower"; type: "float" }
        }
        Signal {
            name: "specularAmountChanged"
            Parameter { name: "specularAmount"; type: "float" }
        }
        Signal {
            name: "specularRoughnessChanged"
            Parameter { name: "specularRoughness"; type: "float" }
        }
        Signal {
            name: "roughnessMapChanged"
            Parameter { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "opacityChanged"
            Parameter { name: "opacity"; type: "float" }
        }
        Signal {
            name: "opacityMapChanged"
            Parameter { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "bumpMapChanged"
            Parameter { name: "bumpMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "bumpAmountChanged"
            Parameter { name: "bumpAmount"; type: "float" }
        }
        Signal {
            name: "normalMapChanged"
            Parameter { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "translucencyMapChanged"
            Parameter { name: "translucencyMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "translucentFalloffChanged"
            Parameter { name: "translucentFalloff"; type: "float" }
        }
        Signal {
            name: "diffuseLightWrapChanged"
            Parameter { name: "diffuseLightWrap"; type: "float" }
        }
        Signal {
            name: "vertexColorsEnabledChanged"
            Parameter { name: "vertexColorsEnabled"; type: "bool" }
        }
        Signal {
            name: "roughnessChannelChanged"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "opacityChannelChanged"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "translucencyChannelChanged"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal { name: "pointSizeChanged" }
        Signal { name: "lineWidthChanged" }
        Method {
            name: "setLighting"
            Parameter { name: "lighting"; type: "QQuick3DDefaultMaterial::Lighting" }
        }
        Method {
            name: "setBlendMode"
            Parameter { name: "blendMode"; type: "QQuick3DDefaultMaterial::BlendMode" }
        }
        Method {
            name: "setDiffuseColor"
            Parameter { name: "diffuseColor"; type: "QColor" }
        }
        Method {
            name: "setDiffuseMap"
            Parameter { name: "diffuseMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setEmissiveFactor"
            Parameter { name: "emissiveFactor"; type: "QVector3D" }
        }
        Method {
            name: "setEmissiveMap"
            Parameter { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularReflectionMap"
            Parameter { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularMap"
            Parameter { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularModel"
            Parameter { name: "specularModel"; type: "QQuick3DDefaultMaterial::SpecularModel" }
        }
        Method {
            name: "setSpecularTint"
            Parameter { name: "specularTint"; type: "QColor" }
        }
        Method {
            name: "setIndexOfRefraction"
            Parameter { name: "indexOfRefraction"; type: "float" }
        }
        Method {
            name: "setFresnelPower"
            Parameter { name: "fresnelPower"; type: "float" }
        }
        Method {
            name: "setSpecularAmount"
            Parameter { name: "specularAmount"; type: "float" }
        }
        Method {
            name: "setSpecularRoughness"
            Parameter { name: "specularRoughness"; type: "float" }
        }
        Method {
            name: "setRoughnessMap"
            Parameter { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setOpacity"
            Parameter { name: "opacity"; type: "float" }
        }
        Method {
            name: "setOpacityMap"
            Parameter { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setBumpMap"
            Parameter { name: "bumpMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setBumpAmount"
            Parameter { name: "bumpAmount"; type: "float" }
        }
        Method {
            name: "setNormalMap"
            Parameter { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setTranslucencyMap"
            Parameter { name: "translucencyMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setTranslucentFalloff"
            Parameter { name: "translucentFalloff"; type: "float" }
        }
        Method {
            name: "setDiffuseLightWrap"
            Parameter { name: "diffuseLightWrap"; type: "float" }
        }
        Method {
            name: "setVertexColorsEnabled"
            Parameter { name: "vertexColorsEnabled"; type: "bool" }
        }
        Method {
            name: "setRoughnessChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setOpacityChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setTranslucencyChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setPointSize"
            Parameter { name: "size"; type: "float" }
        }
        Method {
            name: "setLineWidth"
            Parameter { name: "width"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3ddirectionallight_p.h"
        name: "QQuick3DDirectionalLight"
        accessSemantics: "reference"
        prototype: "QQuick3DAbstractLight"
        exports: [
            "QtQuick3D/DirectionalLight 6.0",
            "QtQuick3D/DirectionalLight 6.8",
            "QtQuick3D/DirectionalLight 6.9"
        ]
        exportMetaObjectRevisions: [1536, 1544, 1545]
        Property {
            name: "csmSplit1"
            revision: 1544
            type: "float"
            read: "csmSplit1"
            write: "setCsmSplit1"
            notify: "csmSplit1Changed"
            index: 0
            isFinal: true
        }
        Property {
            name: "csmSplit2"
            revision: 1544
            type: "float"
            read: "csmSplit2"
            write: "setCsmSplit2"
            notify: "csmSplit2Changed"
            index: 1
            isFinal: true
        }
        Property {
            name: "csmSplit3"
            revision: 1544
            type: "float"
            read: "csmSplit3"
            write: "setCsmSplit3"
            notify: "csmSplit3Changed"
            index: 2
            isFinal: true
        }
        Property {
            name: "csmNumSplits"
            revision: 1544
            type: "int"
            read: "csmNumSplits"
            write: "setCsmNumSplits"
            notify: "csmNumSplitsChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "csmBlendRatio"
            revision: 1544
            type: "float"
            read: "csmBlendRatio"
            write: "setCsmBlendRatio"
            notify: "csmBlendRatioChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "lockShadowmapTexels"
            revision: 1545
            type: "bool"
            read: "lockShadowmapTexels"
            write: "setLockShadowmapTexels"
            notify: "lockShadowmapTexelsChanged"
            index: 5
            isFinal: true
        }
        Signal { name: "csmSplit1Changed"; revision: 1544 }
        Signal { name: "csmSplit2Changed"; revision: 1544 }
        Signal { name: "csmSplit3Changed"; revision: 1544 }
        Signal { name: "csmNumSplitsChanged"; revision: 1544 }
        Signal { name: "csmBlendRatioChanged"; revision: 1544 }
        Signal { name: "lockShadowmapTexelsChanged"; revision: 1545 }
    }
    Component {
        file: "private/qquick3deffect_p.h"
        name: "QQuick3DEffect"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/Effect 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "passes"
            type: "QQuick3DShaderUtilsRenderPass"
            isList: true
            read: "passes"
            index: 0
            isReadonly: true
        }
        Method { name: "onPropertyDirty" }
        Method { name: "onTextureDirty" }
        Method { name: "onPassDirty" }
    }
    Component {
        file: "private/qquick3dinstancing_p.h"
        name: "QQuick3DFileInstancing"
        accessSemantics: "reference"
        prototype: "QQuick3DInstancing"
        exports: [
            "QtQuick3D/FileInstancing 6.2",
            "QtQuick3D/FileInstancing 6.3",
            "QtQuick3D/FileInstancing 6.9"
        ]
        exportMetaObjectRevisions: [1538, 1539, 1545]
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "instanceCount"
            type: "int"
            read: "instanceCount"
            notify: "instanceCountChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "instanceCountChanged" }
        Signal { name: "sourceChanged" }
    }
    Component {
        file: "private/qquick3dfog_p.h"
        name: "QQuick3DFog"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D/Fog 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "enabled"
            type: "bool"
            read: "isEnabled"
            write: "setEnabled"
            notify: "enabledChanged"
            index: 0
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
        }
        Property {
            name: "density"
            type: "float"
            read: "density"
            write: "setDensity"
            notify: "densityChanged"
            index: 2
        }
        Property {
            name: "depthEnabled"
            type: "bool"
            read: "isDepthEnabled"
            write: "setDepthEnabled"
            notify: "depthEnabledChanged"
            index: 3
        }
        Property {
            name: "depthNear"
            type: "float"
            read: "depthNear"
            write: "setDepthNear"
            notify: "depthNearChanged"
            index: 4
        }
        Property {
            name: "depthFar"
            type: "float"
            read: "depthFar"
            write: "setDepthFar"
            notify: "depthFarChanged"
            index: 5
        }
        Property {
            name: "depthCurve"
            type: "float"
            read: "depthCurve"
            write: "setDepthCurve"
            notify: "depthCurveChanged"
            index: 6
        }
        Property {
            name: "heightEnabled"
            type: "bool"
            read: "isHeightEnabled"
            write: "setHeightEnabled"
            notify: "heightEnabledChanged"
            index: 7
        }
        Property {
            name: "leastIntenseY"
            type: "float"
            read: "leastIntenseY"
            write: "setLeastIntenseY"
            notify: "leastIntenseYChanged"
            index: 8
        }
        Property {
            name: "mostIntenseY"
            type: "float"
            read: "mostIntenseY"
            write: "setMostIntenseY"
            notify: "mostIntenseYChanged"
            index: 9
        }
        Property {
            name: "heightCurve"
            type: "float"
            read: "heightCurve"
            write: "setHeightCurve"
            notify: "heightCurveChanged"
            index: 10
        }
        Property {
            name: "transmitEnabled"
            type: "bool"
            read: "isTransmitEnabled"
            write: "setTransmitEnabled"
            notify: "transmitEnabledChanged"
            index: 11
        }
        Property {
            name: "transmitCurve"
            type: "float"
            read: "transmitCurve"
            write: "setTransmitCurve"
            notify: "transmitCurveChanged"
            index: 12
        }
        Signal { name: "changed" }
        Signal { name: "enabledChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "densityChanged" }
        Signal { name: "depthEnabledChanged" }
        Signal { name: "depthNearChanged" }
        Signal { name: "depthFarChanged" }
        Signal { name: "depthCurveChanged" }
        Signal { name: "heightEnabledChanged" }
        Signal { name: "leastIntenseYChanged" }
        Signal { name: "mostIntenseYChanged" }
        Signal { name: "heightCurveChanged" }
        Signal { name: "transmitEnabledChanged" }
        Signal { name: "transmitCurveChanged" }
        Method {
            name: "setEnabled"
            Parameter { name: "newEnabled"; type: "bool" }
        }
        Method {
            name: "setColor"
            Parameter { name: "newColor"; type: "QColor" }
        }
        Method {
            name: "setDensity"
            Parameter { name: "newDensity"; type: "float" }
        }
        Method {
            name: "setDepthEnabled"
            Parameter { name: "newDepthEnabled"; type: "bool" }
        }
        Method {
            name: "setDepthNear"
            Parameter { name: "newDepthNear"; type: "float" }
        }
        Method {
            name: "setDepthFar"
            Parameter { name: "newDepthFar"; type: "float" }
        }
        Method {
            name: "setDepthCurve"
            Parameter { name: "newDepthCurve"; type: "float" }
        }
        Method {
            name: "setHeightEnabled"
            Parameter { name: "newHeightEnabled"; type: "bool" }
        }
        Method {
            name: "setLeastIntenseY"
            Parameter { name: "newleastIntenseY"; type: "float" }
        }
        Method {
            name: "setMostIntenseY"
            Parameter { name: "newmostIntenseY"; type: "float" }
        }
        Method {
            name: "setHeightCurve"
            Parameter { name: "newHeightCurve"; type: "float" }
        }
        Method {
            name: "setTransmitEnabled"
            Parameter { name: "newTransmitEnabled"; type: "bool" }
        }
        Method {
            name: "setTransmitCurve"
            Parameter { name: "newTransmitCurve"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dfrustumcamera_p.h"
        name: "QQuick3DFrustumCamera"
        accessSemantics: "reference"
        prototype: "QQuick3DPerspectiveCamera"
        exports: [
            "QtQuick3D/FrustumCamera 6.0",
            "QtQuick3D/FrustumCamera 6.4",
            "QtQuick3D/FrustumCamera 6.5"
        ]
        exportMetaObjectRevisions: [1536, 1540, 1541]
        Property { name: "top"; type: "float"; read: "top"; write: "setTop"; notify: "topChanged"; index: 0 }
        Property {
            name: "bottom"
            type: "float"
            read: "bottom"
            write: "setBottom"
            notify: "bottomChanged"
            index: 1
        }
        Property {
            name: "right"
            type: "float"
            read: "right"
            write: "setRight"
            notify: "rightChanged"
            index: 2
        }
        Property {
            name: "left"
            type: "float"
            read: "left"
            write: "setLeft"
            notify: "leftChanged"
            index: 3
        }
        Signal { name: "topChanged" }
        Signal { name: "bottomChanged" }
        Signal { name: "rightChanged" }
        Signal { name: "leftChanged" }
        Method {
            name: "setTop"
            Parameter { name: "top"; type: "float" }
        }
        Method {
            name: "setBottom"
            Parameter { name: "bottom"; type: "float" }
        }
        Method {
            name: "setRight"
            Parameter { name: "right"; type: "float" }
        }
        Method {
            name: "setLeft"
            Parameter { name: "left"; type: "float" }
        }
    }
    Component {
        file: "qquick3dgeometry.h"
        name: "QQuick3DGeometry"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/Geometry 6.0", "QtQuick3D/Geometry 6.7"]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1543]
        Signal { name: "geometryNodeDirty" }
        Signal { name: "geometryChanged"; revision: 1543 }
    }
    Component {
        file: "private/qquick3dinstancing_p.h"
        name: "QQuick3DInstanceList"
        accessSemantics: "reference"
        defaultProperty: "instances"
        prototype: "QQuick3DInstancing"
        exports: [
            "QtQuick3D/InstanceList 6.2",
            "QtQuick3D/InstanceList 6.3",
            "QtQuick3D/InstanceList 6.9"
        ]
        exportMetaObjectRevisions: [1538, 1539, 1545]
        Property {
            name: "instances"
            type: "QQuick3DInstanceListEntry"
            isList: true
            read: "instances"
            index: 0
            isReadonly: true
        }
        Property {
            name: "instanceCount"
            type: "int"
            read: "instanceCount"
            notify: "instanceCountChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "instanceCountChanged" }
        Method { name: "handleInstanceChange" }
        Method {
            name: "onInstanceDestroyed"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qquick3dinstancing_p.h"
        name: "QQuick3DInstanceListEntry"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/InstanceListEntry 6.2"]
        exportMetaObjectRevisions: [1538]
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
        }
        Property {
            name: "scale"
            type: "QVector3D"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 1
        }
        Property {
            name: "eulerRotation"
            type: "QVector3D"
            read: "eulerRotation"
            write: "setEulerRotation"
            notify: "eulerRotationChanged"
            index: 2
        }
        Property {
            name: "rotation"
            type: "QQuaternion"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 3
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 4
        }
        Property {
            name: "customData"
            type: "QVector4D"
            read: "customData"
            write: "setCustomData"
            notify: "customDataChanged"
            index: 5
        }
        Signal { name: "positionChanged" }
        Signal { name: "scaleChanged" }
        Signal { name: "eulerRotationChanged" }
        Signal { name: "rotationChanged" }
        Signal { name: "colorChanged" }
        Signal { name: "customDataChanged" }
        Signal { name: "changed" }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "QVector3D" }
        }
        Method {
            name: "setEulerRotation"
            Parameter { name: "eulerRotation"; type: "QVector3D" }
        }
        Method {
            name: "setRotation"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Method {
            name: "setColor"
            Parameter { name: "color"; type: "QColor" }
        }
        Method {
            name: "setCustomData"
            Parameter { name: "customData"; type: "QVector4D" }
        }
    }
    Component {
        file: "qquick3dinstancing.h"
        name: "QQuick3DInstancing"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: [
            "QtQuick3D/Instancing 6.2",
            "QtQuick3D/Instancing 6.3",
            "QtQuick3D/Instancing 6.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1538, 1539, 1545]
        Property {
            name: "instanceCountOverride"
            type: "int"
            read: "instanceCountOverride"
            write: "setInstanceCountOverride"
            notify: "instanceCountOverrideChanged"
            index: 0
        }
        Property {
            name: "hasTransparency"
            type: "bool"
            read: "hasTransparency"
            write: "setHasTransparency"
            notify: "hasTransparencyChanged"
            index: 1
        }
        Property {
            name: "depthSortingEnabled"
            type: "bool"
            read: "depthSortingEnabled"
            write: "setDepthSortingEnabled"
            notify: "depthSortingEnabledChanged"
            index: 2
        }
        Property {
            name: "shadowBoundsMinimum"
            revision: 1545
            type: "QVector3D"
            read: "shadowBoundsMinimum"
            write: "setShadowBoundsMinimum"
            notify: "shadowBoundsMinimumChanged"
            index: 3
        }
        Property {
            name: "shadowBoundsMaximum"
            revision: 1545
            type: "QVector3D"
            read: "shadowBoundsMaximum"
            write: "setShadowBoundsMaximum"
            notify: "shadowBoundsMaximumChanged"
            index: 4
        }
        Signal { name: "instanceTableChanged" }
        Signal { name: "instanceNodeDirty" }
        Signal { name: "instanceCountOverrideChanged" }
        Signal { name: "hasTransparencyChanged" }
        Signal { name: "depthSortingEnabledChanged" }
        Signal { name: "shadowBoundsMinimumChanged"; revision: 1545 }
        Signal { name: "shadowBoundsMaximumChanged"; revision: 1545 }
        Method {
            name: "setInstanceCountOverride"
            Parameter { name: "instanceCountOverride"; type: "int" }
        }
        Method {
            name: "setHasTransparency"
            Parameter { name: "hasTransparency"; type: "bool" }
        }
        Method {
            name: "setDepthSortingEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setShadowBoundsMinimum"
            revision: 1545
            Parameter { name: "newShadowBoundsMinimum"; type: "QVector3D" }
        }
        Method {
            name: "setShadowBoundsMaximum"
            revision: 1545
            Parameter { name: "newShadowBoundsMinimum"; type: "QVector3D" }
        }
        Method {
            name: "instancePosition"
            revision: 1539
            type: "QVector3D"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "instanceScale"
            revision: 1539
            type: "QVector3D"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "instanceRotation"
            revision: 1539
            type: "QQuaternion"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "instanceColor"
            revision: 1539
            type: "QColor"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "instanceCustomData"
            revision: 1539
            type: "QVector4D"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qquick3djoint_p.h"
        name: "QQuick3DJoint"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D/Joint 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "index"
            type: "int"
            read: "index"
            write: "setIndex"
            notify: "indexChanged"
            index: 0
        }
        Property {
            name: "skeletonRoot"
            type: "QQuick3DSkeleton"
            isPointer: true
            read: "skeletonRoot"
            write: "setSkeletonRoot"
            notify: "skeletonRootChanged"
            index: 1
        }
        Signal { name: "indexChanged" }
        Signal { name: "skeletonRootChanged" }
        Method {
            name: "setIndex"
            Parameter { name: "index"; type: "int" }
        }
        Method {
            name: "setSkeletonRoot"
            Parameter { name: "skeleton"; type: "QQuick3DSkeleton"; isPointer: true }
        }
    }
    Component {
        file: "private/qquick3dlightmapper_p.h"
        name: "QQuick3DLightmapper"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D/Lightmapper 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "opacityThreshold"
            type: "float"
            read: "opacityThreshold"
            write: "setOpacityThreshold"
            notify: "opacityThresholdChanged"
            index: 0
        }
        Property {
            name: "bias"
            type: "float"
            read: "bias"
            write: "setBias"
            notify: "biasChanged"
            index: 1
        }
        Property {
            name: "adaptiveBiasEnabled"
            type: "bool"
            read: "isAdaptiveBiasEnabled"
            write: "setAdaptiveBiasEnabled"
            notify: "adaptiveBiasEnabledChanged"
            index: 2
        }
        Property {
            name: "indirectLightEnabled"
            type: "bool"
            read: "isIndirectLightEnabled"
            write: "setIndirectLightEnabled"
            notify: "indirectLightEnabledChanged"
            index: 3
        }
        Property {
            name: "samples"
            type: "int"
            read: "samples"
            write: "setSamples"
            notify: "samplesChanged"
            index: 4
        }
        Property {
            name: "indirectLightWorkgroupSize"
            type: "int"
            read: "indirectLightWorkgroupSize"
            write: "setIndirectLightWorkgroupSize"
            notify: "indirectLightWorkgroupSizeChanged"
            index: 5
        }
        Property {
            name: "bounces"
            type: "int"
            read: "bounces"
            write: "setBounces"
            notify: "bouncesChanged"
            index: 6
        }
        Property {
            name: "indirectLightFactor"
            type: "float"
            read: "indirectLightFactor"
            write: "setIndirectLightFactor"
            notify: "indirectLightFactorChanged"
            index: 7
        }
        Signal { name: "changed" }
        Signal { name: "opacityThresholdChanged" }
        Signal { name: "biasChanged" }
        Signal { name: "adaptiveBiasEnabledChanged" }
        Signal { name: "indirectLightEnabledChanged" }
        Signal { name: "samplesChanged" }
        Signal { name: "indirectLightWorkgroupSizeChanged" }
        Signal { name: "bouncesChanged" }
        Signal { name: "indirectLightFactorChanged" }
        Method {
            name: "setOpacityThreshold"
            Parameter { name: "opacity"; type: "float" }
        }
        Method {
            name: "setBias"
            Parameter { name: "bias"; type: "float" }
        }
        Method {
            name: "setAdaptiveBiasEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setIndirectLightEnabled"
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setSamples"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "setIndirectLightWorkgroupSize"
            Parameter { name: "size"; type: "int" }
        }
        Method {
            name: "setBounces"
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "setIndirectLightFactor"
            Parameter { name: "factor"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dloader_p.h"
        name: "QQuick3DLoader"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D/Loader3D 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property {
            name: "active"
            type: "bool"
            read: "active"
            write: "setActive"
            notify: "activeChanged"
            index: 0
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 1
        }
        Property {
            name: "sourceComponent"
            type: "QQmlComponent"
            isPointer: true
            read: "sourceComponent"
            write: "setSourceComponent"
            reset: "resetSourceComponent"
            notify: "sourceComponentChanged"
            index: 2
        }
        Property {
            name: "item"
            type: "QObject"
            isPointer: true
            read: "item"
            notify: "itemChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "status"
            type: "Status"
            read: "status"
            notify: "statusChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "progress"
            type: "double"
            read: "progress"
            notify: "progressChanged"
            index: 5
            isReadonly: true
        }
        Property {
            name: "asynchronous"
            type: "bool"
            read: "asynchronous"
            write: "setAsynchronous"
            notify: "asynchronousChanged"
            index: 6
        }
        Signal { name: "itemChanged" }
        Signal { name: "activeChanged" }
        Signal { name: "sourceChanged" }
        Signal { name: "sourceComponentChanged" }
        Signal { name: "statusChanged" }
        Signal { name: "progressChanged" }
        Signal { name: "loaded" }
        Signal { name: "asynchronousChanged" }
        Method { name: "sourceLoaded" }
        Method { name: "setSource"; isJavaScriptFunction: true }
    }
    Component {
        file: "private/qquick3dmaterial_p.h"
        name: "QQuick3DMaterial"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/Material 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "CullMode"
            values: ["BackFaceCulling", "FrontFaceCulling", "NoCulling"]
        }
        Enum {
            name: "TextureChannelMapping"
            values: ["R", "G", "B", "A"]
        }
        Enum {
            name: "DepthDrawMode"
            values: [
                "OpaqueOnlyDepthDraw",
                "AlwaysDepthDraw",
                "NeverDepthDraw",
                "OpaquePrePassDepthDraw"
            ]
        }
        Enum {
            name: "VertexColorMask"
            values: [
                "NoMask",
                "RoughnessMask",
                "NormalStrengthMask",
                "SpecularAmountMask",
                "ClearcoatAmountMask",
                "ClearcoatRoughnessAmountMask",
                "ClearcoatNormalStrengthMask",
                "HeightAmountMask",
                "MetalnessMask",
                "OcclusionAmountMask",
                "ThicknessFactorMask",
                "TransmissionFactorMask"
            ]
        }
        Enum {
            name: "VertexColorMaskFlags"
            alias: "VertexColorMask"
            isFlag: true
            values: [
                "NoMask",
                "RoughnessMask",
                "NormalStrengthMask",
                "SpecularAmountMask",
                "ClearcoatAmountMask",
                "ClearcoatRoughnessAmountMask",
                "ClearcoatNormalStrengthMask",
                "HeightAmountMask",
                "MetalnessMask",
                "OcclusionAmountMask",
                "ThicknessFactorMask",
                "TransmissionFactorMask"
            ]
        }
        Property {
            name: "lightProbe"
            type: "QQuick3DTexture"
            isPointer: true
            read: "lightProbe"
            write: "setLightProbe"
            notify: "lightProbeChanged"
            index: 0
        }
        Property {
            name: "cullMode"
            type: "CullMode"
            read: "cullMode"
            write: "setCullMode"
            notify: "cullModeChanged"
            index: 1
        }
        Property {
            name: "depthDrawMode"
            type: "DepthDrawMode"
            read: "depthDrawMode"
            write: "setDepthDrawMode"
            notify: "depthDrawModeChanged"
            index: 2
        }
        Signal {
            name: "lightProbeChanged"
            Parameter { name: "lightProbe"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "cullModeChanged"
            Parameter { name: "cullMode"; type: "QQuick3DMaterial::CullMode" }
        }
        Signal {
            name: "depthDrawModeChanged"
            Parameter { name: "depthDrawMode"; type: "QQuick3DMaterial::DepthDrawMode" }
        }
        Method {
            name: "setLightProbe"
            Parameter { name: "lightProbe"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setCullMode"
            Parameter { name: "cullMode"; type: "QQuick3DMaterial::CullMode" }
        }
        Method {
            name: "setDepthDrawMode"
            Parameter { name: "depthDrawMode"; type: "QQuick3DMaterial::DepthDrawMode" }
        }
    }
    Component {
        file: "private/qquick3dmodel_p.h"
        name: "QQuick3DModel"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: [
            "QtQuick3D/Model 6.0",
            "QtQuick3D/Model 6.3",
            "QtQuick3D/Model 6.4",
            "QtQuick3D/Model 6.5"
        ]
        exportMetaObjectRevisions: [1536, 1539, 1540, 1541]
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "castsShadows"
            type: "bool"
            read: "castsShadows"
            write: "setCastsShadows"
            notify: "castsShadowsChanged"
            index: 1
        }
        Property {
            name: "receivesShadows"
            type: "bool"
            read: "receivesShadows"
            write: "setReceivesShadows"
            notify: "receivesShadowsChanged"
            index: 2
        }
        Property {
            name: "materials"
            type: "QQuick3DMaterial"
            isList: true
            read: "materials"
            index: 3
            isReadonly: true
        }
        Property {
            name: "morphTargets"
            type: "QQuick3DMorphTarget"
            isList: true
            read: "morphTargets"
            notify: "morphTargetsChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "pickable"
            type: "bool"
            read: "pickable"
            write: "setPickable"
            notify: "pickableChanged"
            index: 5
        }
        Property {
            name: "geometry"
            type: "QQuick3DGeometry"
            isPointer: true
            read: "geometry"
            write: "setGeometry"
            notify: "geometryChanged"
            index: 6
        }
        Property {
            name: "instancing"
            type: "QQuick3DInstancing"
            isPointer: true
            read: "instancing"
            write: "setInstancing"
            notify: "instancingChanged"
            index: 7
        }
        Property {
            name: "instanceRoot"
            type: "QQuick3DNode"
            isPointer: true
            read: "instanceRoot"
            write: "setInstanceRoot"
            notify: "instanceRootChanged"
            index: 8
        }
        Property {
            name: "skeleton"
            type: "QQuick3DSkeleton"
            isPointer: true
            read: "skeleton"
            write: "setSkeleton"
            notify: "skeletonChanged"
            index: 9
        }
        Property {
            name: "skin"
            revision: 1540
            type: "QQuick3DSkin"
            isPointer: true
            read: "skin"
            write: "setSkin"
            notify: "skinChanged"
            index: 10
        }
        Property {
            name: "inverseBindPoses"
            type: "QMatrix4x4"
            isList: true
            read: "inverseBindPoses"
            write: "setInverseBindPoses"
            notify: "inverseBindPosesChanged"
            index: 11
        }
        Property {
            name: "bounds"
            type: "QQuick3DBounds3"
            read: "bounds"
            notify: "boundsChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "depthBias"
            type: "float"
            read: "depthBias"
            write: "setDepthBias"
            notify: "depthBiasChanged"
            index: 13
        }
        Property {
            name: "receivesReflections"
            revision: 1539
            type: "bool"
            read: "receivesReflections"
            write: "setReceivesReflections"
            notify: "receivesReflectionsChanged"
            index: 14
        }
        Property {
            name: "castsReflections"
            revision: 1540
            type: "bool"
            read: "castsReflections"
            write: "setCastsReflections"
            notify: "castsReflectionsChanged"
            index: 15
        }
        Property {
            name: "usedInBakedLighting"
            revision: 1540
            type: "bool"
            read: "isUsedInBakedLighting"
            write: "setUsedInBakedLighting"
            notify: "usedInBakedLightingChanged"
            index: 16
        }
        Property {
            name: "lightmapBaseResolution"
            revision: 1540
            type: "int"
            read: "lightmapBaseResolution"
            write: "setLightmapBaseResolution"
            notify: "lightmapBaseResolutionChanged"
            index: 17
        }
        Property {
            name: "bakedLightmap"
            revision: 1540
            type: "QQuick3DBakedLightmap"
            isPointer: true
            read: "bakedLightmap"
            write: "setBakedLightmap"
            notify: "bakedLightmapChanged"
            index: 18
        }
        Property {
            name: "instancingLodMin"
            revision: 1541
            type: "float"
            read: "instancingLodMin"
            write: "setInstancingLodMin"
            notify: "instancingLodMinChanged"
            index: 19
        }
        Property {
            name: "instancingLodMax"
            revision: 1541
            type: "float"
            read: "instancingLodMax"
            write: "setInstancingLodMax"
            notify: "instancingLodMaxChanged"
            index: 20
        }
        Property {
            name: "levelOfDetailBias"
            revision: 1541
            type: "float"
            read: "levelOfDetailBias"
            write: "setLevelOfDetailBias"
            notify: "levelOfDetailBiasChanged"
            index: 21
        }
        Signal { name: "sourceChanged" }
        Signal { name: "castsShadowsChanged" }
        Signal { name: "receivesShadowsChanged" }
        Signal { name: "pickableChanged" }
        Signal { name: "geometryChanged" }
        Signal { name: "skeletonChanged" }
        Signal { name: "inverseBindPosesChanged" }
        Signal { name: "boundsChanged" }
        Signal { name: "instancingChanged" }
        Signal { name: "instanceRootChanged" }
        Signal { name: "morphTargetsChanged" }
        Signal { name: "depthBiasChanged" }
        Signal { name: "receivesReflectionsChanged"; revision: 1539 }
        Signal { name: "castsReflectionsChanged"; revision: 1540 }
        Signal { name: "skinChanged"; revision: 1540 }
        Signal { name: "usedInBakedLightingChanged"; revision: 1540 }
        Signal { name: "lightmapBaseResolutionChanged"; revision: 1540 }
        Signal { name: "bakedLightmapChanged"; revision: 1540 }
        Signal { name: "instancingLodMinChanged"; revision: 1541 }
        Signal { name: "instancingLodMaxChanged"; revision: 1541 }
        Signal { name: "levelOfDetailBiasChanged"; revision: 1541 }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setCastsShadows"
            Parameter { name: "castsShadows"; type: "bool" }
        }
        Method {
            name: "setReceivesShadows"
            Parameter { name: "receivesShadows"; type: "bool" }
        }
        Method {
            name: "setPickable"
            Parameter { name: "pickable"; type: "bool" }
        }
        Method {
            name: "setGeometry"
            Parameter { name: "geometry"; type: "QQuick3DGeometry"; isPointer: true }
        }
        Method {
            name: "setSkeleton"
            Parameter { name: "skeleton"; type: "QQuick3DSkeleton"; isPointer: true }
        }
        Method {
            name: "setInverseBindPoses"
            Parameter { name: "poses"; type: "QMatrix4x4"; isList: true }
        }
        Method {
            name: "setBounds"
            Parameter { name: "min"; type: "QVector3D" }
            Parameter { name: "max"; type: "QVector3D" }
        }
        Method {
            name: "setInstancing"
            Parameter { name: "instancing"; type: "QQuick3DInstancing"; isPointer: true }
        }
        Method {
            name: "setInstanceRoot"
            Parameter { name: "instanceRoot"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setDepthBias"
            Parameter { name: "bias"; type: "float" }
        }
        Method {
            name: "setReceivesReflections"
            revision: 1539
            Parameter { name: "receivesReflections"; type: "bool" }
        }
        Method {
            name: "setCastsReflections"
            revision: 1540
            Parameter { name: "castsReflections"; type: "bool" }
        }
        Method {
            name: "setSkin"
            revision: 1540
            Parameter { name: "skin"; type: "QQuick3DSkin"; isPointer: true }
        }
        Method {
            name: "setUsedInBakedLighting"
            revision: 1540
            Parameter { name: "enable"; type: "bool" }
        }
        Method {
            name: "setLightmapBaseResolution"
            revision: 1540
            Parameter { name: "resolution"; type: "int" }
        }
        Method {
            name: "setBakedLightmap"
            revision: 1540
            Parameter { name: "bakedLightmap"; type: "QQuick3DBakedLightmap"; isPointer: true }
        }
        Method {
            name: "setInstancingLodMin"
            revision: 1541
            Parameter { name: "minDistance"; type: "float" }
        }
        Method {
            name: "setInstancingLodMax"
            revision: 1541
            Parameter { name: "maxDistance"; type: "float" }
        }
        Method {
            name: "setLevelOfDetailBias"
            revision: 1541
            Parameter { name: "newLevelOfDetailBias"; type: "float" }
        }
        Method {
            name: "onMaterialDestroyed"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onMorphTargetDestroyed"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qquick3dmorphtarget_p.h"
        name: "QQuick3DMorphTarget"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/MorphTarget 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "MorphTargetAttribute"
            isScoped: true
            values: [
                "Position",
                "Normal",
                "Tangent",
                "Binormal",
                "TexCoord0",
                "TexCoord1",
                "Color"
            ]
        }
        Enum {
            name: "MorphTargetAttributes"
            alias: "MorphTargetAttribute"
            isFlag: true
            isScoped: true
            values: [
                "Position",
                "Normal",
                "Tangent",
                "Binormal",
                "TexCoord0",
                "TexCoord1",
                "Color"
            ]
        }
        Property {
            name: "weight"
            type: "float"
            read: "weight"
            write: "setWeight"
            notify: "weightChanged"
            index: 0
        }
        Property {
            name: "attributes"
            type: "MorphTargetAttributes"
            read: "attributes"
            write: "setAttributes"
            notify: "attributesChanged"
            index: 1
        }
        Signal { name: "weightChanged" }
        Signal { name: "attributesChanged" }
        Method {
            name: "setWeight"
            Parameter { name: "castsShadows"; type: "float" }
        }
        Method {
            name: "setAttributes"
            Parameter { name: "attributes"; type: "QQuick3DMorphTarget::MorphTargetAttributes" }
        }
    }
    Component {
        file: "private/qquick3dnode_p.h"
        name: "QQuick3DNode"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/Node 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "TransformSpace"
            values: ["LocalSpace", "ParentSpace", "SceneSpace"]
        }
        Enum {
            name: "StaticFlags"
            values: ["None"]
        }
        Property { name: "x"; type: "float"; read: "x"; write: "setX"; notify: "xChanged"; index: 0 }
        Property { name: "y"; type: "float"; read: "y"; write: "setY"; notify: "yChanged"; index: 1 }
        Property { name: "z"; type: "float"; read: "z"; write: "setZ"; notify: "zChanged"; index: 2 }
        Property {
            name: "rotation"
            type: "QQuaternion"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 3
        }
        Property {
            name: "eulerRotation"
            type: "QVector3D"
            read: "eulerRotation"
            write: "setEulerRotation"
            notify: "eulerRotationChanged"
            index: 4
        }
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 5
        }
        Property {
            name: "scale"
            type: "QVector3D"
            read: "scale"
            write: "setScale"
            notify: "scaleChanged"
            index: 6
        }
        Property {
            name: "pivot"
            type: "QVector3D"
            read: "pivot"
            write: "setPivot"
            notify: "pivotChanged"
            index: 7
        }
        Property {
            name: "opacity"
            type: "float"
            read: "localOpacity"
            write: "setLocalOpacity"
            notify: "localOpacityChanged"
            index: 8
        }
        Property {
            name: "visible"
            type: "bool"
            read: "visible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 9
        }
        Property {
            name: "forward"
            type: "QVector3D"
            read: "forward"
            notify: "forwardChanged"
            index: 10
            isReadonly: true
        }
        Property {
            name: "up"
            type: "QVector3D"
            read: "up"
            notify: "upChanged"
            index: 11
            isReadonly: true
        }
        Property {
            name: "right"
            type: "QVector3D"
            read: "right"
            notify: "rightChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "scenePosition"
            type: "QVector3D"
            read: "scenePosition"
            notify: "scenePositionChanged"
            index: 13
            isReadonly: true
        }
        Property {
            name: "sceneRotation"
            type: "QQuaternion"
            read: "sceneRotation"
            notify: "sceneRotationChanged"
            index: 14
            isReadonly: true
        }
        Property {
            name: "sceneScale"
            type: "QVector3D"
            read: "sceneScale"
            notify: "sceneScaleChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "sceneTransform"
            type: "QMatrix4x4"
            read: "sceneTransform"
            notify: "sceneTransformChanged"
            index: 16
            isReadonly: true
        }
        Property {
            name: "staticFlags"
            type: "int"
            read: "staticFlags"
            write: "setStaticFlags"
            notify: "staticFlagsChanged"
            index: 17
        }
        Signal { name: "xChanged" }
        Signal { name: "yChanged" }
        Signal { name: "zChanged" }
        Signal { name: "rotationChanged" }
        Signal { name: "eulerRotationChanged" }
        Signal { name: "positionChanged" }
        Signal { name: "scaleChanged" }
        Signal { name: "pivotChanged" }
        Signal { name: "localOpacityChanged" }
        Signal { name: "visibleChanged" }
        Signal { name: "forwardChanged" }
        Signal { name: "upChanged" }
        Signal { name: "rightChanged" }
        Signal { name: "sceneTransformChanged" }
        Signal { name: "scenePositionChanged" }
        Signal { name: "sceneRotationChanged" }
        Signal { name: "sceneScaleChanged" }
        Signal { name: "staticFlagsChanged" }
        Method {
            name: "setX"
            Parameter { name: "x"; type: "float" }
        }
        Method {
            name: "setY"
            Parameter { name: "y"; type: "float" }
        }
        Method {
            name: "setZ"
            Parameter { name: "z"; type: "float" }
        }
        Method {
            name: "setRotation"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Method {
            name: "setEulerRotation"
            Parameter { name: "eulerRotation"; type: "QVector3D" }
        }
        Method {
            name: "setPosition"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Method {
            name: "setScale"
            Parameter { name: "scale"; type: "QVector3D" }
        }
        Method {
            name: "setPivot"
            Parameter { name: "pivot"; type: "QVector3D" }
        }
        Method {
            name: "setLocalOpacity"
            Parameter { name: "opacity"; type: "float" }
        }
        Method {
            name: "setVisible"
            Parameter { name: "visible"; type: "bool" }
        }
        Method {
            name: "setStaticFlags"
            Parameter { name: "staticFlags"; type: "int" }
        }
        Method {
            name: "rotate"
            Parameter { name: "degrees"; type: "double" }
            Parameter { name: "axis"; type: "QVector3D" }
            Parameter { name: "space"; type: "QQuick3DNode::TransformSpace" }
        }
        Method {
            name: "mapPositionToScene"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "localPosition"; type: "QVector3D" }
        }
        Method {
            name: "mapPositionFromScene"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "scenePosition"; type: "QVector3D" }
        }
        Method {
            name: "mapPositionToNode"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true; isTypeConstant: true }
            Parameter { name: "localPosition"; type: "QVector3D" }
        }
        Method {
            name: "mapPositionFromNode"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true; isTypeConstant: true }
            Parameter { name: "localPosition"; type: "QVector3D" }
        }
        Method {
            name: "mapDirectionToScene"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
        Method {
            name: "mapDirectionFromScene"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "sceneDirection"; type: "QVector3D" }
        }
        Method {
            name: "mapDirectionToNode"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true; isTypeConstant: true }
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
        Method {
            name: "mapDirectionFromNode"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "node"; type: "QQuick3DNode"; isPointer: true; isTypeConstant: true }
            Parameter { name: "localDirection"; type: "QVector3D" }
        }
    }
    Component {
        file: "qquick3dobject.h"
        name: "QQuick3DObject"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QObject"
        interfaces: ["QQmlParserStatus"]
        exports: ["QtQuick3D/Object3D 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Property {
            name: "parent"
            type: "QQuick3DObject"
            isPointer: true
            read: "parentItem"
            write: "setParentItem"
            notify: "parentChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "data"
            type: "QObject"
            isList: true
            read: "data"
            index: 1
            privateClass: "QQuick3DObjectPrivate"
            isReadonly: true
        }
        Property {
            name: "resources"
            type: "QObject"
            isList: true
            read: "resources"
            index: 2
            privateClass: "QQuick3DObjectPrivate"
            isReadonly: true
        }
        Property {
            name: "children"
            type: "QQuick3DObject"
            isList: true
            read: "children"
            notify: "childrenChanged"
            index: 3
            privateClass: "QQuick3DObjectPrivate"
            isReadonly: true
        }
        Property {
            name: "states"
            type: "QQuickState"
            isList: true
            read: "states"
            index: 4
            privateClass: "QQuick3DObjectPrivate"
            isReadonly: true
        }
        Property {
            name: "transitions"
            type: "QQuickTransition"
            isList: true
            read: "transitions"
            index: 5
            privateClass: "QQuick3DObjectPrivate"
            isReadonly: true
        }
        Property {
            name: "state"
            type: "QString"
            read: "state"
            write: "setState"
            notify: "stateChanged"
            index: 6
        }
        Signal { name: "parentChanged" }
        Signal { name: "childrenChanged" }
        Signal { name: "stateChanged" }
        Method { name: "update" }
        Method {
            name: "setParentItem"
            Parameter { name: "parentItem"; type: "QQuick3DObject"; isPointer: true }
        }
        Method {
            name: "_q_resourceObjectDeleted"
            Parameter { type: "QObject"; isPointer: true }
        }
        Method {
            name: "_q_createJSWrapper"
            type: "qulonglong"
            Parameter { type: "QQmlV4ExecutionEnginePtr" }
        }
        Method { name: "_q_cleanupContentItem2D" }
    }
    Component {
        file: "private/qquick3dorthographiccamera_p.h"
        name: "QQuick3DOrthographicCamera"
        accessSemantics: "reference"
        prototype: "QQuick3DCamera"
        exports: [
            "QtQuick3D/OrthographicCamera 6.0",
            "QtQuick3D/OrthographicCamera 6.4",
            "QtQuick3D/OrthographicCamera 6.5"
        ]
        exportMetaObjectRevisions: [1536, 1540, 1541]
        Property {
            name: "clipNear"
            type: "float"
            read: "clipNear"
            write: "setClipNear"
            notify: "clipNearChanged"
            index: 0
        }
        Property {
            name: "clipFar"
            type: "float"
            read: "clipFar"
            write: "setClipFar"
            notify: "clipFarChanged"
            index: 1
        }
        Property {
            name: "horizontalMagnification"
            type: "float"
            read: "horizontalMagnification"
            write: "setHorizontalMagnification"
            notify: "horizontalMagnificationChanged"
            index: 2
        }
        Property {
            name: "verticalMagnification"
            type: "float"
            read: "verticalMagnification"
            write: "setVerticalMagnification"
            notify: "verticalMagnificationChanged"
            index: 3
        }
        Signal { name: "clipNearChanged" }
        Signal { name: "clipFarChanged" }
        Signal { name: "horizontalMagnificationChanged" }
        Signal { name: "verticalMagnificationChanged" }
        Method {
            name: "setClipNear"
            Parameter { name: "clipNear"; type: "float" }
        }
        Method {
            name: "setClipFar"
            Parameter { name: "clipFar"; type: "float" }
        }
        Method {
            name: "setHorizontalMagnification"
            Parameter { name: "horizontalMagnification"; type: "float" }
        }
        Method {
            name: "setVerticalMagnification"
            Parameter { name: "horizontalMagnification"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dperspectivecamera_p.h"
        name: "QQuick3DPerspectiveCamera"
        accessSemantics: "reference"
        prototype: "QQuick3DCamera"
        exports: [
            "QtQuick3D/PerspectiveCamera 6.0",
            "QtQuick3D/PerspectiveCamera 6.4",
            "QtQuick3D/PerspectiveCamera 6.5"
        ]
        exportMetaObjectRevisions: [1536, 1540, 1541]
        Enum {
            name: "FieldOfViewOrientation"
            values: ["Vertical", "Horizontal"]
        }
        Property {
            name: "clipNear"
            type: "float"
            read: "clipNear"
            write: "setClipNear"
            notify: "clipNearChanged"
            index: 0
        }
        Property {
            name: "clipFar"
            type: "float"
            read: "clipFar"
            write: "setClipFar"
            notify: "clipFarChanged"
            index: 1
        }
        Property {
            name: "fieldOfView"
            type: "float"
            read: "fieldOfView"
            write: "setFieldOfView"
            notify: "fieldOfViewChanged"
            index: 2
        }
        Property {
            name: "fieldOfViewOrientation"
            type: "FieldOfViewOrientation"
            read: "fieldOfViewOrientation"
            write: "setFieldOfViewOrientation"
            notify: "fieldOfViewOrientationChanged"
            index: 3
        }
        Signal { name: "clipNearChanged" }
        Signal { name: "clipFarChanged" }
        Signal { name: "fieldOfViewChanged" }
        Signal { name: "fieldOfViewOrientationChanged" }
        Method {
            name: "setClipNear"
            Parameter { name: "clipNear"; type: "float" }
        }
        Method {
            name: "setClipFar"
            Parameter { name: "clipFar"; type: "float" }
        }
        Method {
            name: "setFieldOfView"
            Parameter { name: "fieldOfView"; type: "float" }
        }
        Method {
            name: "setFieldOfViewOrientation"
            Parameter {
                name: "fieldOfViewOrientation"
                type: "QQuick3DPerspectiveCamera::FieldOfViewOrientation"
            }
        }
    }
    Component {
        file: "private/qquick3dpickresult_p.h"
        name: "QQuick3DPickResult"
        accessSemantics: "value"
        exports: ["QtQuick3D/pickResult 6.0", "QtQuick3D/pickResult 6.8"]
        isCreatable: false
        exportMetaObjectRevisions: [1536, 1544]
        Property {
            name: "objectHit"
            type: "QQuick3DModel"
            isPointer: true
            read: "objectHit"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "distance"
            type: "float"
            read: "distance"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "uvPosition"
            type: "QVector2D"
            read: "uvPosition"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "scenePosition"
            type: "QVector3D"
            read: "scenePosition"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "normal"
            type: "QVector3D"
            read: "normal"
            index: 5
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "sceneNormal"
            type: "QVector3D"
            read: "sceneNormal"
            index: 6
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "instanceIndex"
            type: "int"
            read: "instanceIndex"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "itemHit"
            revision: 1544
            type: "QQuickItem"
            isPointer: true
            read: "itemHit"
            index: 8
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "hitType"
            revision: 1544
            type: "QQuick3DPickResultEnums::HitType"
            read: "hitType"
            index: 9
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/qquick3dpickresult_p.h"
        name: "QQuick3DPickResultEnums"
        accessSemantics: "none"
        exports: ["QtQuick3D/PickResult 6.8"]
        isCreatable: false
        exportMetaObjectRevisions: [1544]
        Enum {
            name: "HitType"
            values: ["Null", "Model", "Item"]
        }
    }
    Component {
        file: "private/qquick3dpointlight_p.h"
        name: "QQuick3DPointLight"
        accessSemantics: "reference"
        prototype: "QQuick3DAbstractLight"
        exports: [
            "QtQuick3D/PointLight 6.0",
            "QtQuick3D/PointLight 6.8",
            "QtQuick3D/PointLight 6.9"
        ]
        exportMetaObjectRevisions: [1536, 1544, 1545]
        Property {
            name: "constantFade"
            type: "float"
            read: "constantFade"
            write: "setConstantFade"
            notify: "constantFadeChanged"
            index: 0
        }
        Property {
            name: "linearFade"
            type: "float"
            read: "linearFade"
            write: "setLinearFade"
            notify: "linearFadeChanged"
            index: 1
        }
        Property {
            name: "quadraticFade"
            type: "float"
            read: "quadraticFade"
            write: "setQuadraticFade"
            notify: "quadraticFadeChanged"
            index: 2
        }
        Signal { name: "constantFadeChanged" }
        Signal { name: "linearFadeChanged" }
        Signal { name: "quadraticFadeChanged" }
        Method {
            name: "setConstantFade"
            Parameter { name: "constantFade"; type: "float" }
        }
        Method {
            name: "setLinearFade"
            Parameter { name: "linearFade"; type: "float" }
        }
        Method {
            name: "setQuadraticFade"
            Parameter { name: "quadraticFade"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dprincipledmaterial_p.h"
        name: "QQuick3DPrincipledMaterial"
        accessSemantics: "reference"
        prototype: "QQuick3DMaterial"
        exports: [
            "QtQuick3D/PrincipledMaterial 6.0",
            "QtQuick3D/PrincipledMaterial 6.2",
            "QtQuick3D/PrincipledMaterial 6.3",
            "QtQuick3D/PrincipledMaterial 6.5",
            "QtQuick3D/PrincipledMaterial 6.8"
        ]
        exportMetaObjectRevisions: [1536, 1538, 1539, 1541, 1544]
        Enum {
            name: "Lighting"
            values: ["NoLighting", "FragmentLighting"]
        }
        Enum {
            name: "BlendMode"
            values: ["SourceOver", "Screen", "Multiply"]
        }
        Enum {
            name: "AlphaMode"
            values: ["Default", "Mask", "Blend", "Opaque"]
        }
        Enum {
            name: "VertexColorMask"
            values: [
                "NoMask",
                "RoughnessMask",
                "NormalStrengthMask",
                "SpecularAmountMask",
                "ClearcoatAmountMask",
                "ClearcoatRoughnessAmountMask",
                "ClearcoatNormalStrengthMask",
                "HeightAmountMask",
                "MetalnessMask",
                "OcclusionAmountMask",
                "ThicknessFactorMask",
                "TransmissionFactorMask"
            ]
        }
        Enum {
            name: "VertexColorMaskFlags"
            alias: "VertexColorMask"
            isFlag: true
            values: [
                "NoMask",
                "RoughnessMask",
                "NormalStrengthMask",
                "SpecularAmountMask",
                "ClearcoatAmountMask",
                "ClearcoatRoughnessAmountMask",
                "ClearcoatNormalStrengthMask",
                "HeightAmountMask",
                "MetalnessMask",
                "OcclusionAmountMask",
                "ThicknessFactorMask",
                "TransmissionFactorMask"
            ]
        }
        Property {
            name: "lighting"
            type: "Lighting"
            read: "lighting"
            write: "setLighting"
            notify: "lightingChanged"
            index: 0
        }
        Property {
            name: "blendMode"
            type: "BlendMode"
            read: "blendMode"
            write: "setBlendMode"
            notify: "blendModeChanged"
            index: 1
        }
        Property {
            name: "baseColor"
            type: "QColor"
            read: "baseColor"
            write: "setBaseColor"
            notify: "baseColorChanged"
            index: 2
        }
        Property {
            name: "baseColorMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "baseColorMap"
            write: "setBaseColorMap"
            notify: "baseColorMapChanged"
            index: 3
        }
        Property {
            name: "baseColorSingleChannelEnabled"
            revision: 1544
            type: "bool"
            read: "baseColorSingleChannelEnabled"
            write: "setBaseColorSingleChannelEnabled"
            notify: "baseColorSingleChannelEnabledChanged"
            index: 4
        }
        Property {
            name: "baseColorChannel"
            revision: 1544
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "baseColorChannel"
            write: "setBaseColorChannel"
            notify: "baseColorChannelChanged"
            index: 5
        }
        Property {
            name: "metalness"
            type: "float"
            read: "metalness"
            write: "setMetalness"
            notify: "metalnessChanged"
            index: 6
        }
        Property {
            name: "metalnessMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "metalnessMap"
            write: "setMetalnessMap"
            notify: "metalnessMapChanged"
            index: 7
        }
        Property {
            name: "metalnessChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "metalnessChannel"
            write: "setMetalnessChannel"
            notify: "metalnessChannelChanged"
            index: 8
        }
        Property {
            name: "specularAmount"
            type: "float"
            read: "specularAmount"
            write: "setSpecularAmount"
            notify: "specularAmountChanged"
            index: 9
        }
        Property {
            name: "specularMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "specularMap"
            write: "setSpecularMap"
            notify: "specularMapChanged"
            index: 10
        }
        Property {
            name: "specularTint"
            type: "float"
            read: "specularTint"
            write: "setSpecularTint"
            notify: "specularTintChanged"
            index: 11
        }
        Property {
            name: "specularSingleChannelEnabled"
            revision: 1544
            type: "bool"
            read: "specularSingleChannelEnabled"
            write: "setSpecularSingleChannelEnabled"
            notify: "specularSingleChannelEnabledChanged"
            index: 12
        }
        Property {
            name: "specularChannel"
            revision: 1544
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "specularChannel"
            write: "setSpecularChannel"
            notify: "specularChannelChanged"
            index: 13
        }
        Property {
            name: "roughness"
            type: "float"
            read: "roughness"
            write: "setRoughness"
            notify: "roughnessChanged"
            index: 14
        }
        Property {
            name: "roughnessMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "roughnessMap"
            write: "setRoughnessMap"
            notify: "roughnessMapChanged"
            index: 15
        }
        Property {
            name: "roughnessChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "roughnessChannel"
            write: "setRoughnessChannel"
            notify: "roughnessChannelChanged"
            index: 16
        }
        Property {
            name: "emissiveFactor"
            type: "QVector3D"
            read: "emissiveFactor"
            write: "setEmissiveFactor"
            notify: "emissiveFactorChanged"
            index: 17
        }
        Property {
            name: "emissiveMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "emissiveMap"
            write: "setEmissiveMap"
            notify: "emissiveMapChanged"
            index: 18
        }
        Property {
            name: "emissiveSingleChannelEnabled"
            revision: 1544
            type: "bool"
            read: "emissiveSingleChannelEnabled"
            write: "setEmissiveSingleChannelEnabled"
            notify: "emissiveSingleChannelEnabledChanged"
            index: 19
        }
        Property {
            name: "emissiveChannel"
            revision: 1544
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "emissiveChannel"
            write: "setEmissiveChannel"
            notify: "emissiveChannelChanged"
            index: 20
        }
        Property {
            name: "invertOpacityMapValue"
            revision: 1544
            type: "bool"
            read: "invertOpacityMapValue"
            write: "setInvertOpacityMapValue"
            notify: "invertOpacityMapValueChanged"
            index: 21
        }
        Property {
            name: "opacity"
            type: "float"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 22
        }
        Property {
            name: "opacityMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "opacityMap"
            write: "setOpacityMap"
            notify: "opacityMapChanged"
            index: 23
        }
        Property {
            name: "opacityChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "opacityChannel"
            write: "setOpacityChannel"
            notify: "opacityChannelChanged"
            index: 24
        }
        Property {
            name: "normalMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "normalMap"
            write: "setNormalMap"
            notify: "normalMapChanged"
            index: 25
        }
        Property {
            name: "normalStrength"
            type: "float"
            read: "normalStrength"
            write: "setNormalStrength"
            notify: "normalStrengthChanged"
            index: 26
        }
        Property {
            name: "specularReflectionMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "specularReflectionMap"
            write: "setSpecularReflectionMap"
            notify: "specularReflectionMapChanged"
            index: 27
        }
        Property {
            name: "occlusionMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "occlusionMap"
            write: "setOcclusionMap"
            notify: "occlusionMapChanged"
            index: 28
        }
        Property {
            name: "occlusionChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "occlusionChannel"
            write: "setOcclusionChannel"
            notify: "occlusionChannelChanged"
            index: 29
        }
        Property {
            name: "occlusionAmount"
            type: "float"
            read: "occlusionAmount"
            write: "setOcclusionAmount"
            notify: "occlusionAmountChanged"
            index: 30
        }
        Property {
            name: "alphaMode"
            type: "AlphaMode"
            read: "alphaMode"
            write: "setAlphaMode"
            notify: "alphaModeChanged"
            index: 31
        }
        Property {
            name: "alphaCutoff"
            type: "float"
            read: "alphaCutoff"
            write: "setAlphaCutoff"
            notify: "alphaCutoffChanged"
            index: 32
        }
        Property {
            name: "pointSize"
            type: "float"
            read: "pointSize"
            write: "setPointSize"
            notify: "pointSizeChanged"
            index: 33
        }
        Property {
            name: "lineWidth"
            type: "float"
            read: "lineWidth"
            write: "setLineWidth"
            notify: "lineWidthChanged"
            index: 34
        }
        Property {
            name: "heightMap"
            revision: 1538
            type: "QQuick3DTexture"
            isPointer: true
            read: "heightMap"
            write: "setHeightMap"
            notify: "heightMapChanged"
            index: 35
        }
        Property {
            name: "heightChannel"
            revision: 1538
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "heightChannel"
            write: "setHeightChannel"
            notify: "heightChannelChanged"
            index: 36
        }
        Property {
            name: "heightAmount"
            revision: 1538
            type: "float"
            read: "heightAmount"
            write: "setHeightAmount"
            notify: "heightAmountChanged"
            index: 37
        }
        Property {
            name: "minHeightMapSamples"
            revision: 1538
            type: "int"
            read: "minHeightMapSamples"
            write: "setMinHeightMapSamples"
            notify: "minHeightMapSamplesChanged"
            index: 38
        }
        Property {
            name: "maxHeightMapSamples"
            revision: 1538
            type: "int"
            read: "maxHeightMapSamples"
            write: "setMaxHeightMapSamples"
            notify: "maxHeightMapSamplesChanged"
            index: 39
        }
        Property {
            name: "clearcoatAmount"
            revision: 1539
            type: "float"
            read: "clearcoatAmount"
            write: "setClearcoatAmount"
            notify: "clearcoatAmountChanged"
            index: 40
        }
        Property {
            name: "clearcoatMap"
            revision: 1539
            type: "QQuick3DTexture"
            isPointer: true
            read: "clearcoatMap"
            write: "setClearcoatMap"
            notify: "clearcoatMapChanged"
            index: 41
        }
        Property {
            name: "clearcoatChannel"
            revision: 1539
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "clearcoatChannel"
            write: "setClearcoatChannel"
            notify: "clearcoatChannelChanged"
            index: 42
        }
        Property {
            name: "clearcoatRoughnessAmount"
            revision: 1539
            type: "float"
            read: "clearcoatRoughnessAmount"
            write: "setClearcoatRoughnessAmount"
            notify: "clearcoatRoughnessAmountChanged"
            index: 43
        }
        Property {
            name: "clearcoatRoughnessChannel"
            revision: 1539
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "clearcoatRoughnessChannel"
            write: "setClearcoatRoughnessChannel"
            notify: "clearcoatRoughnessChannelChanged"
            index: 44
        }
        Property {
            name: "clearcoatRoughnessMap"
            revision: 1539
            type: "QQuick3DTexture"
            isPointer: true
            read: "clearcoatRoughnessMap"
            write: "setClearcoatRoughnessMap"
            notify: "clearcoatRoughnessMapChanged"
            index: 45
        }
        Property {
            name: "clearcoatNormalMap"
            revision: 1539
            type: "QQuick3DTexture"
            isPointer: true
            read: "clearcoatNormalMap"
            write: "setClearcoatNormalMap"
            notify: "clearcoatNormalMapChanged"
            index: 46
        }
        Property {
            name: "clearcoatNormalStrength"
            revision: 1544
            type: "float"
            read: "clearcoatNormalStrength"
            write: "setClearcoatNormalStrength"
            notify: "clearcoatNormalStrengthChanged"
            index: 47
        }
        Property {
            name: "transmissionFactor"
            type: "float"
            read: "transmissionFactor"
            write: "setTransmissionFactor"
            notify: "transmissionFactorChanged"
            index: 48
        }
        Property {
            name: "transmissionMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "transmissionMap"
            write: "setTransmissionMap"
            notify: "transmissionMapChanged"
            index: 49
        }
        Property {
            name: "transmissionChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "transmissionChannel"
            write: "setTransmissionChannel"
            notify: "transmissionChannelChanged"
            index: 50
        }
        Property {
            name: "thicknessFactor"
            revision: 1539
            type: "float"
            read: "thicknessFactor"
            write: "setThicknessFactor"
            notify: "thicknessFactorChanged"
            index: 51
        }
        Property {
            name: "thicknessMap"
            revision: 1539
            type: "QQuick3DTexture"
            isPointer: true
            read: "thicknessMap"
            write: "setThicknessMap"
            notify: "thicknessMapChanged"
            index: 52
        }
        Property {
            name: "thicknessChannel"
            revision: 1539
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "thicknessChannel"
            write: "setThicknessChannel"
            notify: "thicknessChannelChanged"
            index: 53
        }
        Property {
            name: "attenuationDistance"
            revision: 1539
            type: "float"
            read: "attenuationDistance"
            write: "setAttenuationDistance"
            notify: "attenuationDistanceChanged"
            index: 54
        }
        Property {
            name: "attenuationColor"
            revision: 1539
            type: "QColor"
            read: "attenuationColor"
            write: "setAttenuationColor"
            notify: "attenuationColorChanged"
            index: 55
        }
        Property {
            name: "indexOfRefraction"
            revision: 1539
            type: "float"
            read: "indexOfRefraction"
            write: "setIndexOfRefraction"
            notify: "indexOfRefractionChanged"
            index: 56
        }
        Property {
            name: "vertexColorsEnabled"
            revision: 1541
            type: "bool"
            read: "vertexColorsEnabled"
            write: "setVertexColorsEnabled"
            notify: "vertexColorsEnabledChanged"
            index: 57
        }
        Property {
            name: "fresnelScaleBiasEnabled"
            revision: 1544
            type: "bool"
            read: "fresnelScaleBiasEnabled"
            write: "setFresnelScaleBiasEnabled"
            notify: "fresnelScaleBiasEnabledChanged"
            index: 58
        }
        Property {
            name: "fresnelScale"
            revision: 1544
            type: "float"
            read: "fresnelScale"
            write: "setFresnelScale"
            notify: "fresnelScaleChanged"
            index: 59
        }
        Property {
            name: "fresnelBias"
            revision: 1544
            type: "float"
            read: "fresnelBias"
            write: "setFresnelBias"
            notify: "fresnelBiasChanged"
            index: 60
        }
        Property {
            name: "fresnelPower"
            revision: 1544
            type: "float"
            read: "fresnelPower"
            write: "setFresnelPower"
            notify: "fresnelPowerChanged"
            index: 61
        }
        Property {
            name: "clearcoatFresnelScaleBiasEnabled"
            revision: 1544
            type: "bool"
            read: "clearcoatFresnelScaleBiasEnabled"
            write: "setClearcoatFresnelScaleBiasEnabled"
            notify: "clearcoatFresnelScaleBiasEnabledChanged"
            index: 62
        }
        Property {
            name: "clearcoatFresnelScale"
            revision: 1544
            type: "float"
            read: "clearcoatFresnelScale"
            write: "setClearcoatFresnelScale"
            notify: "clearcoatFresnelScaleChanged"
            index: 63
        }
        Property {
            name: "clearcoatFresnelBias"
            revision: 1544
            type: "float"
            read: "clearcoatFresnelBias"
            write: "setClearcoatFresnelBias"
            notify: "clearcoatFresnelBiasChanged"
            index: 64
        }
        Property {
            name: "clearcoatFresnelPower"
            revision: 1544
            type: "float"
            read: "clearcoatFresnelPower"
            write: "setClearcoatFresnelPower"
            notify: "clearcoatFresnelPowerChanged"
            index: 65
        }
        Property {
            name: "vertexColorsMaskEnabled"
            revision: 1544
            type: "bool"
            read: "vertexColorsMaskEnabled"
            write: "setVertexColorsMaskEnabled"
            notify: "vertexColorsMaskEnabledChanged"
            index: 66
        }
        Property {
            name: "vertexColorRedMask"
            revision: 1544
            type: "VertexColorMaskFlags"
            read: "vertexColorRedMask"
            write: "setVertexColorRedMask"
            notify: "vertexColorRedMaskChanged"
            index: 67
        }
        Property {
            name: "vertexColorGreenMask"
            revision: 1544
            type: "VertexColorMaskFlags"
            read: "vertexColorGreenMask"
            write: "setVertexColorGreenMask"
            notify: "vertexColorGreenMaskChanged"
            index: 68
        }
        Property {
            name: "vertexColorBlueMask"
            revision: 1544
            type: "VertexColorMaskFlags"
            read: "vertexColorBlueMask"
            write: "setVertexColorBlueMask"
            notify: "vertexColorBlueMaskChanged"
            index: 69
        }
        Property {
            name: "vertexColorAlphaMask"
            revision: 1544
            type: "VertexColorMaskFlags"
            read: "vertexColorAlphaMask"
            write: "setVertexColorAlphaMask"
            notify: "vertexColorAlphaMaskChanged"
            index: 70
        }
        Signal {
            name: "lightingChanged"
            Parameter { name: "lighting"; type: "QQuick3DPrincipledMaterial::Lighting" }
        }
        Signal {
            name: "blendModeChanged"
            Parameter { name: "blendMode"; type: "QQuick3DPrincipledMaterial::BlendMode" }
        }
        Signal {
            name: "baseColorChanged"
            Parameter { name: "baseColor"; type: "QColor" }
        }
        Signal {
            name: "baseColorMapChanged"
            Parameter { name: "baseColorMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "baseColorSingleChannelEnabledChanged"
            revision: 1544
            Parameter { name: "baseColorSingleChannelEnabled"; type: "bool" }
        }
        Signal {
            name: "baseColorChannelChanged"
            revision: 1544
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "specularSingleChannelEnabledChanged"
            revision: 1544
            Parameter { name: "specularColorSingleChannelEnabled"; type: "bool" }
        }
        Signal {
            name: "specularChannelChanged"
            revision: 1544
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "emissiveSingleChannelEnabledChanged"
            revision: 1544
            Parameter { name: "emissiveColorSingleChannelEnabled"; type: "bool" }
        }
        Signal {
            name: "emissiveChannelChanged"
            revision: 1544
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "emissiveMapChanged"
            Parameter { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "emissiveFactorChanged"
            Parameter { name: "emissiveFactor"; type: "QVector3D" }
        }
        Signal {
            name: "specularReflectionMapChanged"
            Parameter { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "specularMapChanged"
            Parameter { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "specularTintChanged"
            Parameter { name: "specularTint"; type: "float" }
        }
        Signal {
            name: "specularAmountChanged"
            Parameter { name: "specularAmount"; type: "float" }
        }
        Signal {
            name: "roughnessChanged"
            Parameter { name: "roughness"; type: "float" }
        }
        Signal {
            name: "roughnessMapChanged"
            Parameter { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "invertOpacityMapValueChanged"
            revision: 1544
            Parameter { name: "invertOpacityMapValue"; type: "bool" }
        }
        Signal {
            name: "opacityChanged"
            Parameter { name: "opacity"; type: "float" }
        }
        Signal {
            name: "opacityMapChanged"
            Parameter { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "normalMapChanged"
            Parameter { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "metalnessChanged"
            Parameter { name: "metalness"; type: "float" }
        }
        Signal {
            name: "metalnessMapChanged"
            Parameter { name: "metalnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "normalStrengthChanged"
            Parameter { name: "normalStrength"; type: "float" }
        }
        Signal {
            name: "occlusionMapChanged"
            Parameter { name: "occlusionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "occlusionAmountChanged"
            Parameter { name: "occlusionAmount"; type: "float" }
        }
        Signal {
            name: "alphaModeChanged"
            Parameter { name: "alphaMode"; type: "QQuick3DPrincipledMaterial::AlphaMode" }
        }
        Signal {
            name: "alphaCutoffChanged"
            Parameter { name: "alphaCutoff"; type: "float" }
        }
        Signal {
            name: "metalnessChannelChanged"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "roughnessChannelChanged"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "opacityChannelChanged"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "occlusionChannelChanged"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal { name: "pointSizeChanged" }
        Signal { name: "lineWidthChanged" }
        Signal {
            name: "heightMapChanged"
            revision: 1538
            Parameter { name: "heightMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "heightChannelChanged"
            revision: 1538
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "heightAmountChanged"
            revision: 1538
            Parameter { name: "heightAmount"; type: "float" }
        }
        Signal {
            name: "minHeightMapSamplesChanged"
            revision: 1538
            Parameter { name: "samples"; type: "int" }
        }
        Signal {
            name: "maxHeightMapSamplesChanged"
            revision: 1538
            Parameter { name: "samples"; type: "int" }
        }
        Signal {
            name: "clearcoatAmountChanged"
            revision: 1539
            Parameter { name: "amount"; type: "float" }
        }
        Signal {
            name: "clearcoatMapChanged"
            revision: 1539
            Parameter { name: "texture"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "clearcoatChannelChanged"
            revision: 1539
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "clearcoatRoughnessAmountChanged"
            revision: 1539
            Parameter { name: "amount"; type: "float" }
        }
        Signal {
            name: "clearcoatRoughnessChannelChanged"
            revision: 1539
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "clearcoatRoughnessMapChanged"
            revision: 1539
            Parameter { name: "texture"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "clearcoatNormalMapChanged"
            revision: 1539
            Parameter { name: "texture"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "clearcoatNormalStrengthChanged"
            revision: 1544
            Parameter { name: "clearcoatNormalStrength"; type: "float" }
        }
        Signal {
            name: "transmissionFactorChanged"
            revision: 1539
            Parameter { name: "amount"; type: "float" }
        }
        Signal {
            name: "transmissionMapChanged"
            revision: 1539
            Parameter { name: "texture"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "transmissionChannelChanged"
            revision: 1539
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "thicknessFactorChanged"
            revision: 1539
            Parameter { name: "amount"; type: "float" }
        }
        Signal {
            name: "thicknessMapChanged"
            revision: 1539
            Parameter { name: "texture"; type: "QQuick3DTexture"; isPointer: true }
        }
        Signal {
            name: "thicknessChannelChanged"
            revision: 1539
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Signal {
            name: "attenuationDistanceChanged"
            revision: 1539
            Parameter { name: "distance"; type: "float" }
        }
        Signal {
            name: "attenuationColorChanged"
            revision: 1539
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "indexOfRefractionChanged"
            revision: 1539
            Parameter { name: "indexOfRefraction"; type: "float" }
        }
        Signal {
            name: "fresnelScaleBiasEnabledChanged"
            revision: 1544
            Parameter { name: "fresnelScaleBiasEnabled"; type: "bool" }
        }
        Signal {
            name: "fresnelScaleChanged"
            revision: 1544
            Parameter { name: "fresnelScale"; type: "float" }
        }
        Signal {
            name: "fresnelBiasChanged"
            revision: 1544
            Parameter { name: "fresnelBias"; type: "float" }
        }
        Signal {
            name: "fresnelPowerChanged"
            revision: 1544
            Parameter { name: "fresnelPower"; type: "float" }
        }
        Signal {
            name: "clearcoatFresnelScaleBiasEnabledChanged"
            revision: 1544
            Parameter { name: "clearcoatFresnelScaleBiasEnabled"; type: "bool" }
        }
        Signal {
            name: "clearcoatFresnelScaleChanged"
            revision: 1544
            Parameter { name: "clearcoatFresnelScale"; type: "float" }
        }
        Signal {
            name: "clearcoatFresnelBiasChanged"
            revision: 1544
            Parameter { name: "clearcoatFresnelBias"; type: "float" }
        }
        Signal {
            name: "clearcoatFresnelPowerChanged"
            revision: 1544
            Parameter { name: "clearcoatFresnelPower"; type: "float" }
        }
        Signal {
            name: "vertexColorsEnabledChanged"
            revision: 1541
            Parameter { name: "vertexColorsEnabled"; type: "bool" }
        }
        Signal { name: "vertexColorsMaskEnabledChanged"; revision: 1544 }
        Signal { name: "vertexColorRedMaskChanged"; revision: 1544 }
        Signal { name: "vertexColorGreenMaskChanged"; revision: 1544 }
        Signal { name: "vertexColorBlueMaskChanged"; revision: 1544 }
        Signal { name: "vertexColorAlphaMaskChanged"; revision: 1544 }
        Method {
            name: "setLighting"
            Parameter { name: "lighting"; type: "QQuick3DPrincipledMaterial::Lighting" }
        }
        Method {
            name: "setBlendMode"
            Parameter { name: "blendMode"; type: "QQuick3DPrincipledMaterial::BlendMode" }
        }
        Method {
            name: "setBaseColor"
            Parameter { name: "baseColor"; type: "QColor" }
        }
        Method {
            name: "setBaseColorMap"
            Parameter { name: "baseColorMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setBaseColorSingleChannelEnabled"
            revision: 1544
            Parameter { name: "baseColorSingleChannelEnabled"; type: "bool" }
        }
        Method {
            name: "setBaseColorChannel"
            revision: 1544
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setSpecularSingleChannelEnabled"
            revision: 1544
            Parameter { name: "specularSingleChannelEnabled"; type: "bool" }
        }
        Method {
            name: "setSpecularChannel"
            revision: 1544
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setEmissiveSingleChannelEnabled"
            revision: 1544
            Parameter { name: "emissiveSingleChannelEnabled"; type: "bool" }
        }
        Method {
            name: "setEmissiveChannel"
            revision: 1544
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setEmissiveMap"
            Parameter { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setEmissiveFactor"
            Parameter { name: "emissiveFactor"; type: "QVector3D" }
        }
        Method {
            name: "setSpecularReflectionMap"
            Parameter { name: "specularReflectionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularMap"
            Parameter { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularTint"
            Parameter { name: "specularTint"; type: "float" }
        }
        Method {
            name: "setSpecularAmount"
            Parameter { name: "specularAmount"; type: "float" }
        }
        Method {
            name: "setRoughness"
            Parameter { name: "roughness"; type: "float" }
        }
        Method {
            name: "setRoughnessMap"
            Parameter { name: "roughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setInvertOpacityMapValue"
            revision: 1544
            Parameter { name: "invertOpacityMapValue"; type: "bool" }
        }
        Method {
            name: "setOpacity"
            Parameter { name: "opacity"; type: "float" }
        }
        Method {
            name: "setOpacityMap"
            Parameter { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setNormalMap"
            Parameter { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setMetalness"
            Parameter { name: "metalnessAmount"; type: "float" }
        }
        Method {
            name: "setMetalnessMap"
            Parameter { name: "metalnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setNormalStrength"
            Parameter { name: "normalStrength"; type: "float" }
        }
        Method {
            name: "setOcclusionMap"
            Parameter { name: "occlusionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setOcclusionAmount"
            Parameter { name: "occlusionAmount"; type: "float" }
        }
        Method {
            name: "setAlphaMode"
            Parameter { name: "alphaMode"; type: "QQuick3DPrincipledMaterial::AlphaMode" }
        }
        Method {
            name: "setAlphaCutoff"
            Parameter { name: "alphaCutoff"; type: "float" }
        }
        Method {
            name: "setMetalnessChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setRoughnessChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setOpacityChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setOcclusionChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setPointSize"
            Parameter { name: "size"; type: "float" }
        }
        Method {
            name: "setLineWidth"
            Parameter { name: "width"; type: "float" }
        }
        Method {
            name: "setHeightMap"
            revision: 1538
            Parameter { name: "heightMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setHeightChannel"
            revision: 1538
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setHeightAmount"
            revision: 1538
            Parameter { name: "heightAmount"; type: "float" }
        }
        Method {
            name: "setMinHeightMapSamples"
            revision: 1538
            Parameter { name: "samples"; type: "int" }
        }
        Method {
            name: "setMaxHeightMapSamples"
            revision: 1538
            Parameter { name: "samples"; type: "int" }
        }
        Method {
            name: "setClearcoatAmount"
            revision: 1539
            Parameter { name: "newClearcoatAmount"; type: "float" }
        }
        Method {
            name: "setClearcoatMap"
            revision: 1539
            Parameter { name: "newClearcoatMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setClearcoatChannel"
            revision: 1539
            Parameter { name: "newClearcoatChannel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setClearcoatRoughnessAmount"
            revision: 1539
            Parameter { name: "newClearcoatRoughnessAmount"; type: "float" }
        }
        Method {
            name: "setClearcoatRoughnessChannel"
            revision: 1539
            Parameter {
                name: "newClearcoatRoughnessChannel"
                type: "QQuick3DMaterial::TextureChannelMapping"
            }
        }
        Method {
            name: "setClearcoatRoughnessMap"
            revision: 1539
            Parameter { name: "newClearcoatRoughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setClearcoatNormalMap"
            revision: 1539
            Parameter { name: "newClearcoatNormalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setClearcoatNormalStrength"
            revision: 1544
            Parameter { name: "clearcoatNormalStrength"; type: "float" }
        }
        Method {
            name: "setTransmissionFactor"
            revision: 1539
            Parameter { name: "newTransmissionFactor"; type: "float" }
        }
        Method {
            name: "setTransmissionMap"
            revision: 1539
            Parameter { name: "newTransmissionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setTransmissionChannel"
            revision: 1539
            Parameter { name: "newTransmissionChannel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setThicknessFactor"
            revision: 1539
            Parameter { name: "newThicknessFactor"; type: "float" }
        }
        Method {
            name: "setThicknessMap"
            revision: 1539
            Parameter { name: "newThicknessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setThicknessChannel"
            revision: 1539
            Parameter { name: "newThicknessChannel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setAttenuationDistance"
            revision: 1539
            Parameter { name: "newAttenuationDistance"; type: "float" }
        }
        Method {
            name: "setAttenuationColor"
            revision: 1539
            Parameter { name: "newAttenuationColor"; type: "QColor" }
        }
        Method {
            name: "setIndexOfRefraction"
            revision: 1539
            Parameter { name: "indexOfRefraction"; type: "float" }
        }
        Method {
            name: "setFresnelScaleBiasEnabled"
            revision: 1544
            Parameter { name: "fresnelScaleBias"; type: "bool" }
        }
        Method {
            name: "setFresnelScale"
            revision: 1544
            Parameter { name: "fresnelScale"; type: "float" }
        }
        Method {
            name: "setFresnelBias"
            revision: 1544
            Parameter { name: "fresnelBias"; type: "float" }
        }
        Method {
            name: "setFresnelPower"
            revision: 1544
            Parameter { name: "fresnelPower"; type: "float" }
        }
        Method {
            name: "setClearcoatFresnelScaleBiasEnabled"
            revision: 1544
            Parameter { name: "clearcoatFresnelScaleBias"; type: "bool" }
        }
        Method {
            name: "setClearcoatFresnelScale"
            revision: 1544
            Parameter { name: "clearcoatFresnelScale"; type: "float" }
        }
        Method {
            name: "setClearcoatFresnelBias"
            revision: 1544
            Parameter { name: "clearcoatFresnelBias"; type: "float" }
        }
        Method {
            name: "setClearcoatFresnelPower"
            revision: 1544
            Parameter { name: "clearcoatFresnelPower"; type: "float" }
        }
        Method {
            name: "setVertexColorsEnabled"
            revision: 1541
            Parameter { name: "vertexColorsEnabled"; type: "bool" }
        }
        Method {
            name: "setVertexColorsMaskEnabled"
            revision: 1544
            Parameter { name: "vertexColorsMaskEnabled"; type: "bool" }
        }
        Method {
            name: "setVertexColorRedMask"
            revision: 1544
            Parameter { name: "vertexColorRedMask"; type: "VertexColorMaskFlags" }
        }
        Method {
            name: "setVertexColorGreenMask"
            revision: 1544
            Parameter { name: "vertexColorGreenMask"; type: "VertexColorMaskFlags" }
        }
        Method {
            name: "setVertexColorBlueMask"
            revision: 1544
            Parameter { name: "vertexColorBlueMask"; type: "VertexColorMaskFlags" }
        }
        Method {
            name: "setVertexColorAlphaMask"
            revision: 1544
            Parameter { name: "vertexColorAlphaMask"; type: "VertexColorMaskFlags" }
        }
    }
    Component {
        file: "private/qquick3dquaternionanimation_p.h"
        name: "QQuick3DQuaternionAnimation"
        accessSemantics: "reference"
        prototype: "QQuickPropertyAnimation"
        exports: ["QtQuick3D/QuaternionAnimation 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Type"
            values: ["Slerp", "Nlerp"]
        }
        Property { name: "from"; type: "QQuaternion"; read: "from"; write: "setFrom"; index: 0 }
        Property { name: "to"; type: "QQuaternion"; read: "to"; write: "setTo"; index: 1 }
        Property {
            name: "type"
            type: "Type"
            read: "type"
            write: "setType"
            notify: "typeChanged"
            index: 2
        }
        Property {
            name: "fromXRotation"
            type: "float"
            read: "fromXRotation"
            write: "setFromXRotation"
            notify: "fromXRotationChanged"
            index: 3
        }
        Property {
            name: "fromYRotation"
            type: "float"
            read: "fromYRotation"
            write: "setFromYRotation"
            notify: "fromYRotationChanged"
            index: 4
        }
        Property {
            name: "fromZRotation"
            type: "float"
            read: "fromZRotation"
            write: "setFromZRotation"
            notify: "fromZRotationChanged"
            index: 5
        }
        Property {
            name: "toXRotation"
            type: "float"
            read: "toXRotation"
            write: "setToXRotation"
            notify: "toXRotationChanged"
            index: 6
        }
        Property {
            name: "toYRotation"
            type: "float"
            read: "toYRotation"
            write: "setToYRotation"
            notify: "toYRotationChanged"
            index: 7
        }
        Property {
            name: "toZRotation"
            type: "float"
            read: "toZRotation"
            write: "setToZRotation"
            notify: "toZRotationChanged"
            index: 8
        }
        Signal {
            name: "typeChanged"
            Parameter { name: "type"; type: "QQuick3DQuaternionAnimation::Type" }
        }
        Signal {
            name: "fromXRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "fromYRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "fromZRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "toXRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "toYRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "toZRotationChanged"
            Parameter { name: "value"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dquaternionutils_p.h"
        name: "QQuick3DQuaternionUtils"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D/Quaternion 6.0"]
        isCreatable: false
        isSingleton: true
        exportMetaObjectRevisions: [1536]
        Method {
            name: "fromAxesAndAngles"
            type: "QQuaternion"
            Parameter { name: "axis1"; type: "QVector3D" }
            Parameter { name: "angle1"; type: "float" }
            Parameter { name: "axis2"; type: "QVector3D" }
            Parameter { name: "angle2"; type: "float" }
            Parameter { name: "axis3"; type: "QVector3D" }
            Parameter { name: "angle3"; type: "float" }
        }
        Method {
            name: "fromAxesAndAngles"
            type: "QQuaternion"
            Parameter { name: "axis1"; type: "QVector3D" }
            Parameter { name: "angle1"; type: "float" }
            Parameter { name: "axis2"; type: "QVector3D" }
            Parameter { name: "angle2"; type: "float" }
        }
        Method {
            name: "fromAxisAndAngle"
            type: "QQuaternion"
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
            Parameter { name: "z"; type: "float" }
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "fromAxisAndAngle"
            type: "QQuaternion"
            Parameter { name: "axis"; type: "QVector3D" }
            Parameter { name: "angle"; type: "float" }
        }
        Method {
            name: "fromEulerAngles"
            type: "QQuaternion"
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
            Parameter { name: "z"; type: "float" }
        }
        Method {
            name: "fromEulerAngles"
            type: "QQuaternion"
            Parameter { name: "eulerAngles"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            type: "QQuaternion"
            Parameter { name: "sourcePosition"; type: "QVector3D" }
            Parameter { name: "targetPosition"; type: "QVector3D" }
            Parameter { name: "forwardDirection"; type: "QVector3D" }
            Parameter { name: "upDirection"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            type: "QQuaternion"
            isCloned: true
            Parameter { name: "sourcePosition"; type: "QVector3D" }
            Parameter { name: "targetPosition"; type: "QVector3D" }
            Parameter { name: "forwardDirection"; type: "QVector3D" }
        }
        Method {
            name: "lookAt"
            type: "QQuaternion"
            isCloned: true
            Parameter { name: "sourcePosition"; type: "QVector3D" }
            Parameter { name: "targetPosition"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/qquick3dreflectionprobe_p.h"
        name: "QQuick3DReflectionProbe"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: [
            "QtQuick3D/ReflectionProbe 6.3",
            "QtQuick3D/ReflectionProbe 6.4",
            "QtQuick3D/ReflectionProbe 6.5"
        ]
        exportMetaObjectRevisions: [1539, 1540, 1541]
        Enum {
            name: "ReflectionQuality"
            isScoped: true
            values: ["VeryLow", "Low", "Medium", "High", "VeryHigh"]
        }
        Enum {
            name: "ReflectionRefreshMode"
            isScoped: true
            values: ["FirstFrame", "EveryFrame"]
        }
        Enum {
            name: "ReflectionTimeSlicing"
            isScoped: true
            values: ["None", "AllFacesAtOnce", "IndividualFaces"]
        }
        Property {
            name: "quality"
            type: "ReflectionQuality"
            read: "quality"
            write: "setQuality"
            notify: "qualityChanged"
            index: 0
        }
        Property {
            name: "clearColor"
            type: "QColor"
            read: "clearColor"
            write: "setClearColor"
            notify: "clearColorChanged"
            index: 1
        }
        Property {
            name: "refreshMode"
            type: "ReflectionRefreshMode"
            read: "refreshMode"
            write: "setRefreshMode"
            notify: "refreshModeChanged"
            index: 2
        }
        Property {
            name: "timeSlicing"
            type: "ReflectionTimeSlicing"
            read: "timeSlicing"
            write: "setTimeSlicing"
            notify: "timeSlicingChanged"
            index: 3
        }
        Property {
            name: "parallaxCorrection"
            type: "bool"
            read: "parallaxCorrection"
            write: "setParallaxCorrection"
            notify: "parallaxCorrectionChanged"
            index: 4
        }
        Property {
            name: "boxSize"
            type: "QVector3D"
            read: "boxSize"
            write: "setBoxSize"
            notify: "boxSizeChanged"
            index: 5
        }
        Property {
            name: "boxOffset"
            revision: 1540
            type: "QVector3D"
            read: "boxOffset"
            write: "setBoxOffset"
            notify: "boxOffsetChanged"
            index: 6
        }
        Property {
            name: "debugView"
            revision: 1540
            type: "bool"
            read: "debugView"
            write: "setDebugView"
            notify: "debugViewChanged"
            index: 7
        }
        Property {
            name: "texture"
            revision: 1541
            type: "QQuick3DCubeMapTexture"
            isPointer: true
            read: "texture"
            write: "setTexture"
            notify: "textureChanged"
            index: 8
        }
        Signal { name: "qualityChanged" }
        Signal { name: "clearColorChanged" }
        Signal { name: "refreshModeChanged" }
        Signal { name: "timeSlicingChanged" }
        Signal { name: "parallaxCorrectionChanged" }
        Signal { name: "boxSizeChanged" }
        Signal { name: "debugViewChanged"; revision: 1540 }
        Signal { name: "boxOffsetChanged"; revision: 1540 }
        Signal { name: "textureChanged"; revision: 1541 }
        Method {
            name: "setQuality"
            Parameter { name: "reflectionQuality"; type: "ReflectionQuality" }
        }
        Method {
            name: "setClearColor"
            Parameter { name: "clearColor"; type: "QColor" }
        }
        Method {
            name: "setRefreshMode"
            Parameter { name: "newRefreshMode"; type: "ReflectionRefreshMode" }
        }
        Method {
            name: "setTimeSlicing"
            Parameter { name: "newTimeSlicing"; type: "ReflectionTimeSlicing" }
        }
        Method {
            name: "setParallaxCorrection"
            Parameter { name: "parallaxCorrection"; type: "bool" }
        }
        Method {
            name: "setBoxSize"
            Parameter { name: "newBoxSize"; type: "QVector3D" }
        }
        Method {
            name: "setDebugView"
            revision: 1540
            Parameter { name: "debugView"; type: "bool" }
        }
        Method {
            name: "setBoxOffset"
            revision: 1540
            Parameter { name: "boxOffset"; type: "QVector3D" }
        }
        Method {
            name: "setTexture"
            revision: 1541
            Parameter { name: "newTexture"; type: "QQuick3DCubeMapTexture"; isPointer: true }
        }
        Method { name: "scheduleUpdate"; revision: 1540 }
    }
    Component {
        file: "qquick3drenderextensions.h"
        name: "QQuick3DRenderExtension"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/RenderExtension 6.6"]
        isCreatable: false
        exportMetaObjectRevisions: [1542]
    }
    Component {
        file: "private/qquick3drepeater_p.h"
        name: "QQuick3DRepeater"
        accessSemantics: "reference"
        defaultProperty: "delegate"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D/Repeater3D 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "model"
            type: "QVariant"
            read: "model"
            write: "setModel"
            notify: "modelChanged"
            index: 0
        }
        Property {
            name: "delegate"
            type: "QQmlComponent"
            isPointer: true
            read: "delegate"
            write: "setDelegate"
            notify: "delegateChanged"
            index: 1
        }
        Property {
            name: "count"
            type: "int"
            read: "count"
            notify: "countChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "modelChanged" }
        Signal { name: "delegateChanged" }
        Signal { name: "countChanged" }
        Signal {
            name: "objectAdded"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QQuick3DObject"; isPointer: true }
        }
        Signal {
            name: "objectRemoved"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "object"; type: "QQuick3DObject"; isPointer: true }
        }
        Method {
            name: "createdObject"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "initObject"
            Parameter { type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "modelUpdated"
            Parameter { name: "changeSet"; type: "QQmlChangeSet" }
            Parameter { name: "reset"; type: "bool" }
        }
        Method {
            name: "objectAt"
            type: "QQuick3DObject"
            isPointer: true
            isMethodConstant: true
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qquick3dresourceloader_p.h"
        name: "QQuick3DResourceLoader"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/ResourceLoader 6.3"]
        exportMetaObjectRevisions: [1539]
        Property {
            name: "meshSources"
            type: "QUrl"
            isList: true
            read: "meshSources"
            write: "setMeshSources"
            notify: "meshSourcesChanged"
            index: 0
        }
        Property {
            name: "textures"
            type: "QQuick3DTexture"
            isList: true
            read: "textures"
            index: 1
            isReadonly: true
        }
        Property {
            name: "geometries"
            type: "QQuick3DGeometry"
            isList: true
            read: "geometries"
            index: 2
            isReadonly: true
        }
        Signal { name: "meshSourcesChanged" }
        Method {
            name: "onGeometryDestroyed"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onTextureDestroyed"
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "private/qquick3dsceneenvironment_p.h"
        name: "QQuick3DSceneEnvironment"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: [
            "QtQuick3D/SceneEnvironment 6.0",
            "QtQuick3D/SceneEnvironment 6.4",
            "QtQuick3D/SceneEnvironment 6.5",
            "QtQuick3D/SceneEnvironment 6.9"
        ]
        exportMetaObjectRevisions: [1536, 1540, 1541, 1545]
        Enum {
            name: "QQuick3DEnvironmentAAModeValues"
            values: ["NoAA", "SSAA", "MSAA", "ProgressiveAA"]
        }
        Enum {
            name: "QQuick3DEnvironmentAAQualityValues"
            values: ["Medium", "High", "VeryHigh"]
        }
        Enum {
            name: "QQuick3DEnvironmentBackgroundTypes"
            values: [
                "Transparent",
                "Unspecified",
                "Color",
                "SkyBox",
                "SkyBoxCubeMap"
            ]
        }
        Enum {
            name: "QQuick3DEnvironmentTonemapModes"
            values: [
                "TonemapModeNone",
                "TonemapModeLinear",
                "TonemapModeAces",
                "TonemapModeHejlDawson",
                "TonemapModeFilmic"
            ]
        }
        Enum {
            name: "QQuick3DEnvironmentOITMethod"
            values: ["OITNone", "OITWeightedBlended"]
        }
        Property {
            name: "antialiasingMode"
            type: "QQuick3DEnvironmentAAModeValues"
            read: "antialiasingMode"
            write: "setAntialiasingMode"
            notify: "antialiasingModeChanged"
            index: 0
        }
        Property {
            name: "antialiasingQuality"
            type: "QQuick3DEnvironmentAAQualityValues"
            read: "antialiasingQuality"
            write: "setAntialiasingQuality"
            notify: "antialiasingQualityChanged"
            index: 1
        }
        Property {
            name: "temporalAAEnabled"
            type: "bool"
            read: "temporalAAEnabled"
            write: "setTemporalAAEnabled"
            notify: "temporalAAEnabledChanged"
            index: 2
        }
        Property {
            name: "temporalAAStrength"
            type: "float"
            read: "temporalAAStrength"
            write: "setTemporalAAStrength"
            notify: "temporalAAStrengthChanged"
            index: 3
        }
        Property {
            name: "backgroundMode"
            type: "QQuick3DEnvironmentBackgroundTypes"
            read: "backgroundMode"
            write: "setBackgroundMode"
            notify: "backgroundModeChanged"
            index: 4
        }
        Property {
            name: "clearColor"
            type: "QColor"
            read: "clearColor"
            write: "setClearColor"
            notify: "clearColorChanged"
            index: 5
        }
        Property {
            name: "depthTestEnabled"
            type: "bool"
            read: "depthTestEnabled"
            write: "setDepthTestEnabled"
            notify: "depthTestEnabledChanged"
            index: 6
        }
        Property {
            name: "depthPrePassEnabled"
            type: "bool"
            read: "depthPrePassEnabled"
            write: "setDepthPrePassEnabled"
            notify: "depthPrePassEnabledChanged"
            index: 7
        }
        Property {
            name: "aoStrength"
            type: "float"
            read: "aoStrength"
            write: "setAoStrength"
            notify: "aoStrengthChanged"
            index: 8
        }
        Property {
            name: "aoDistance"
            type: "float"
            read: "aoDistance"
            write: "setAoDistance"
            notify: "aoDistanceChanged"
            index: 9
        }
        Property {
            name: "aoSoftness"
            type: "float"
            read: "aoSoftness"
            write: "setAoSoftness"
            notify: "aoSoftnessChanged"
            index: 10
        }
        Property {
            name: "aoDither"
            type: "bool"
            read: "aoDither"
            write: "setAoDither"
            notify: "aoDitherChanged"
            index: 11
        }
        Property {
            name: "aoSampleRate"
            type: "int"
            read: "aoSampleRate"
            write: "setAoSampleRate"
            notify: "aoSampleRateChanged"
            index: 12
        }
        Property {
            name: "aoBias"
            type: "float"
            read: "aoBias"
            write: "setAoBias"
            notify: "aoBiasChanged"
            index: 13
        }
        Property {
            name: "aoEnabled"
            revision: 1541
            type: "bool"
            read: "aoEnabled"
            write: "setAoEnabled"
            notify: "aoEnabledChanged"
            index: 14
        }
        Property {
            name: "lightProbe"
            type: "QQuick3DTexture"
            isPointer: true
            read: "lightProbe"
            write: "setLightProbe"
            notify: "lightProbeChanged"
            index: 15
        }
        Property {
            name: "probeExposure"
            type: "float"
            read: "probeExposure"
            write: "setProbeExposure"
            notify: "probeExposureChanged"
            index: 16
        }
        Property {
            name: "probeHorizon"
            type: "float"
            read: "probeHorizon"
            write: "setProbeHorizon"
            notify: "probeHorizonChanged"
            index: 17
        }
        Property {
            name: "probeOrientation"
            type: "QVector3D"
            read: "probeOrientation"
            write: "setProbeOrientation"
            notify: "probeOrientationChanged"
            index: 18
        }
        Property {
            name: "skyBoxCubeMap"
            revision: 1540
            type: "QQuick3DCubeMapTexture"
            isPointer: true
            read: "skyBoxCubeMap"
            write: "setSkyBoxCubeMap"
            notify: "skyBoxCubeMapChanged"
            index: 19
        }
        Property {
            name: "tonemapMode"
            type: "QQuick3DEnvironmentTonemapModes"
            read: "tonemapMode"
            write: "setTonemapMode"
            notify: "tonemapModeChanged"
            index: 20
        }
        Property {
            name: "effects"
            type: "QQuick3DEffect"
            isList: true
            read: "effects"
            index: 21
            isReadonly: true
        }
        Property {
            name: "skyboxBlurAmount"
            revision: 1540
            type: "float"
            read: "skyboxBlurAmount"
            write: "setSkyboxBlurAmount"
            notify: "skyboxBlurAmountChanged"
            index: 22
        }
        Property {
            name: "specularAAEnabled"
            revision: 1540
            type: "bool"
            read: "specularAAEnabled"
            write: "setSpecularAAEnabled"
            notify: "specularAAEnabledChanged"
            index: 23
        }
        Property {
            name: "lightmapper"
            revision: 1540
            type: "QQuick3DLightmapper"
            isPointer: true
            read: "lightmapper"
            write: "setLightmapper"
            notify: "lightmapperChanged"
            index: 24
        }
        Property {
            name: "debugSettings"
            revision: 1541
            type: "QQuick3DDebugSettings"
            isPointer: true
            read: "debugSettings"
            write: "setDebugSettings"
            notify: "debugSettingsChanged"
            index: 25
        }
        Property {
            name: "scissorRect"
            revision: 1541
            type: "QRect"
            read: "scissorRect"
            write: "setScissorRect"
            notify: "scissorRectChanged"
            index: 26
        }
        Property {
            name: "fog"
            revision: 1541
            type: "QQuick3DFog"
            isPointer: true
            read: "fog"
            write: "setFog"
            notify: "fogChanged"
            index: 27
        }
        Property {
            name: "oitMethod"
            revision: 1545
            type: "QQuick3DEnvironmentOITMethod"
            read: "oitMethod"
            write: "setOitMethod"
            notify: "oitMethodChanged"
            index: 28
        }
        Signal { name: "antialiasingModeChanged" }
        Signal { name: "antialiasingQualityChanged" }
        Signal { name: "temporalAAEnabledChanged" }
        Signal { name: "temporalAAStrengthChanged" }
        Signal { name: "backgroundModeChanged" }
        Signal { name: "clearColorChanged" }
        Signal { name: "aoStrengthChanged" }
        Signal { name: "aoDistanceChanged" }
        Signal { name: "aoSoftnessChanged" }
        Signal { name: "aoDitherChanged" }
        Signal { name: "aoSampleRateChanged" }
        Signal { name: "aoBiasChanged" }
        Signal { name: "aoEnabledChanged"; revision: 1541 }
        Signal { name: "lightProbeChanged" }
        Signal { name: "probeExposureChanged" }
        Signal { name: "probeHorizonChanged" }
        Signal { name: "probeOrientationChanged" }
        Signal { name: "depthTestEnabledChanged" }
        Signal { name: "depthPrePassEnabledChanged" }
        Signal { name: "tonemapModeChanged" }
        Signal { name: "skyboxBlurAmountChanged"; revision: 1540 }
        Signal { name: "specularAAEnabledChanged"; revision: 1540 }
        Signal { name: "lightmapperChanged"; revision: 1540 }
        Signal { name: "skyBoxCubeMapChanged"; revision: 1540 }
        Signal { name: "debugSettingsChanged"; revision: 1541 }
        Signal { name: "scissorRectChanged"; revision: 1541 }
        Signal { name: "fogChanged"; revision: 1541 }
        Signal { name: "oitMethodChanged"; revision: 1545 }
        Method {
            name: "setAntialiasingMode"
            Parameter {
                name: "antialiasingMode"
                type: "QQuick3DSceneEnvironment::QQuick3DEnvironmentAAModeValues"
            }
        }
        Method {
            name: "setAntialiasingQuality"
            Parameter {
                name: "antialiasingQuality"
                type: "QQuick3DSceneEnvironment::QQuick3DEnvironmentAAQualityValues"
            }
        }
        Method {
            name: "setTemporalAAEnabled"
            Parameter { name: "temporalAAEnabled"; type: "bool" }
        }
        Method {
            name: "setTemporalAAStrength"
            Parameter { name: "strength"; type: "float" }
        }
        Method {
            name: "setBackgroundMode"
            Parameter {
                name: "backgroundMode"
                type: "QQuick3DSceneEnvironment::QQuick3DEnvironmentBackgroundTypes"
            }
        }
        Method {
            name: "setClearColor"
            Parameter { name: "clearColor"; type: "QColor" }
        }
        Method {
            name: "setAoStrength"
            Parameter { name: "aoStrength"; type: "float" }
        }
        Method {
            name: "setAoDistance"
            Parameter { name: "aoDistance"; type: "float" }
        }
        Method {
            name: "setAoSoftness"
            Parameter { name: "aoSoftness"; type: "float" }
        }
        Method {
            name: "setAoDither"
            Parameter { name: "aoDither"; type: "bool" }
        }
        Method {
            name: "setAoSampleRate"
            Parameter { name: "aoSampleRate"; type: "int" }
        }
        Method {
            name: "setAoBias"
            Parameter { name: "aoBias"; type: "float" }
        }
        Method {
            name: "setLightProbe"
            Parameter { name: "lightProbe"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setProbeExposure"
            Parameter { name: "probeExposure"; type: "float" }
        }
        Method {
            name: "setProbeHorizon"
            Parameter { name: "probeHorizon"; type: "float" }
        }
        Method {
            name: "setProbeOrientation"
            Parameter { name: "orientation"; type: "QVector3D" }
        }
        Method {
            name: "setDepthTestEnabled"
            Parameter { name: "depthTestEnabled"; type: "bool" }
        }
        Method {
            name: "setDepthPrePassEnabled"
            Parameter { name: "depthPrePassEnabled"; type: "bool" }
        }
        Method {
            name: "setTonemapMode"
            Parameter {
                name: "tonemapMode"
                type: "QQuick3DSceneEnvironment::QQuick3DEnvironmentTonemapModes"
            }
        }
        Method {
            name: "setSkyboxBlurAmount"
            revision: 1540
            Parameter { name: "newSkyboxBlurAmount"; type: "float" }
        }
        Method {
            name: "setSpecularAAEnabled"
            revision: 1540
            Parameter { name: "enabled"; type: "bool" }
        }
        Method {
            name: "setSkyBoxCubeMap"
            revision: 1540
            Parameter { name: "newSkyBoxCubeMap"; type: "QQuick3DCubeMapTexture"; isPointer: true }
        }
        Method {
            name: "setLightmapper"
            revision: 1540
            Parameter { name: "lightmapper"; type: "QQuick3DLightmapper"; isPointer: true }
        }
        Method {
            name: "setDebugSettings"
            revision: 1541
            Parameter { name: "newDebugSettings"; type: "QQuick3DDebugSettings"; isPointer: true }
        }
        Method {
            name: "setScissorRect"
            revision: 1541
            Parameter { name: "scissorRect"; type: "QRect" }
        }
        Method {
            name: "setFog"
            revision: 1541
            Parameter { name: "fog"; type: "QQuick3DFog"; isPointer: true }
        }
        Method {
            name: "setOitMethod"
            revision: 1545
            Parameter { name: "mode"; type: "QQuick3DSceneEnvironment::QQuick3DEnvironmentOITMethod" }
        }
    }
    Component {
        file: "private/qquick3dshaderutils_p.h"
        name: "QQuick3DShaderUtilsApplyValue"
        accessSemantics: "reference"
        prototype: "QQuick3DShaderUtilsRenderCommand"
        exports: ["QtQuick3D/SetUniformValue 6.0"]
        exportMetaObjectRevisions: [1536]
        Property { name: "target"; type: "QByteArray"; index: 0 }
        Property { name: "value"; type: "QVariant"; index: 1 }
    }
    Component {
        file: "private/qquick3dshaderutils_p.h"
        name: "QQuick3DShaderUtilsBuffer"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D/Buffer 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "TextureFilterOperation"
            isScoped: true
            values: ["Unknown", "Nearest", "Linear"]
        }
        Enum {
            name: "TextureCoordOperation"
            isScoped: true
            values: ["Unknown", "ClampToEdge", "MirroredRepeat", "Repeat"]
        }
        Enum {
            name: "AllocateBufferFlagValues"
            isScoped: true
            values: ["None", "SceneLifetime"]
        }
        Enum {
            name: "TextureFormat"
            isScoped: true
            values: [
                "Unknown",
                "RGBA8",
                "RGBA16F",
                "RGBA32F",
                "R8",
                "R16",
                "R16F",
                "R32F"
            ]
        }
        Property { name: "format"; type: "TextureFormat"; read: "format"; write: "setFormat"; index: 0 }
        Property {
            name: "textureFilterOperation"
            type: "TextureFilterOperation"
            read: "textureFilterOperation"
            write: "setTextureFilterOperation"
            index: 1
        }
        Property {
            name: "textureCoordOperation"
            type: "TextureCoordOperation"
            read: "textureCoordOperation"
            write: "setTextureCoordOperation"
            index: 2
        }
        Property { name: "sizeMultiplier"; type: "float"; index: 3 }
        Property {
            name: "bufferFlags"
            type: "AllocateBufferFlagValues"
            read: "bufferFlags"
            write: "setBufferFlags"
            index: 4
        }
        Property { name: "name"; type: "QByteArray"; index: 5 }
    }
    Component {
        file: "private/qquick3dshaderutils_p.h"
        name: "QQuick3DShaderUtilsBufferInput"
        accessSemantics: "reference"
        prototype: "QQuick3DShaderUtilsRenderCommand"
        exports: ["QtQuick3D/BufferInput 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "buffer"
            type: "QQuick3DShaderUtilsBuffer"
            isPointer: true
            read: "buffer"
            write: "setBuffer"
            index: 0
        }
        Property { name: "sampler"; type: "QByteArray"; index: 1 }
    }
    Component {
        file: "private/qquick3dshaderutils_p.h"
        name: "QQuick3DShaderUtilsRenderCommand"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D/Command 6.0"]
        exportMetaObjectRevisions: [1536]
    }
    Component {
        file: "private/qquick3dshaderutils_p.h"
        name: "QQuick3DShaderUtilsRenderPass"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D/Pass 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "commands"
            type: "QQuick3DShaderUtilsRenderCommand"
            isList: true
            read: "commands"
            index: 0
            isReadonly: true
        }
        Property { name: "output"; type: "QQuick3DShaderUtilsBuffer"; isPointer: true; index: 1 }
        Property {
            name: "shaders"
            type: "QQuick3DShaderUtilsShader"
            isList: true
            read: "shaders"
            index: 2
            isReadonly: true
        }
        Signal { name: "changed" }
    }
    Component {
        file: "private/qquick3dshaderutils_p.h"
        name: "QQuick3DShaderUtilsShader"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D/Shader 6.0"]
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Stage"
            isScoped: true
            type: "quint8"
            values: ["Vertex", "Fragment"]
        }
        Property { name: "shader"; type: "QUrl"; notify: "shaderChanged"; index: 0 }
        Property { name: "stage"; type: "Stage"; notify: "stageChanged"; index: 1 }
        Signal { name: "shaderChanged" }
        Signal { name: "stageChanged" }
    }
    Component {
        file: "private/qquick3dshaderutils_p.h"
        name: "QQuick3DShaderUtilsTextureInput"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: ["QtQuick3D/TextureInput 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "texture"
            type: "QQuick3DTexture"
            isPointer: true
            read: "texture"
            write: "setTexture"
            notify: "textureChanged"
            index: 0
        }
        Property { name: "enabled"; type: "bool"; notify: "enabledChanged"; index: 1 }
        Signal { name: "textureChanged" }
        Signal { name: "enabledChanged" }
        Method {
            name: "setTexture"
            Parameter { name: "texture"; type: "QQuick3DTexture"; isPointer: true }
        }
    }
    Component {
        file: "private/qquick3dskeleton_p.h"
        name: "QQuick3DSkeleton"
        accessSemantics: "reference"
        prototype: "QQuick3DNode"
        exports: ["QtQuick3D/Skeleton 6.0"]
        exportMetaObjectRevisions: [1536]
        Signal { name: "skeletonNodeDirty" }
    }
    Component {
        file: "private/qquick3dskin_p.h"
        name: "QQuick3DSkin"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/Skin 6.0"]
        exportMetaObjectRevisions: [1536]
        Property {
            name: "joints"
            type: "QQuick3DNode"
            isList: true
            read: "joints"
            index: 0
            isReadonly: true
        }
        Property {
            name: "inverseBindPoses"
            type: "QMatrix4x4"
            isList: true
            read: "inverseBindPoses"
            write: "setInverseBindPoses"
            notify: "inverseBindPosesChanged"
            index: 1
        }
        Signal { name: "inverseBindPosesChanged" }
        Method {
            name: "setInverseBindPoses"
            Parameter { name: "poses"; type: "QMatrix4x4"; isList: true }
        }
    }
    Component {
        file: "private/qquick3dspecularglossymaterial_p.h"
        name: "QQuick3DSpecularGlossyMaterial"
        accessSemantics: "reference"
        prototype: "QQuick3DMaterial"
        exports: [
            "QtQuick3D/SpecularGlossyMaterial 6.4",
            "QtQuick3D/SpecularGlossyMaterial 6.5",
            "QtQuick3D/SpecularGlossyMaterial 6.8"
        ]
        exportMetaObjectRevisions: [1540, 1541, 1544]
        Enum {
            name: "Lighting"
            values: ["NoLighting", "FragmentLighting"]
        }
        Enum {
            name: "BlendMode"
            values: ["SourceOver", "Screen", "Multiply"]
        }
        Enum {
            name: "AlphaMode"
            values: ["Default", "Mask", "Blend", "Opaque"]
        }
        Enum {
            name: "VertexColorMask"
            values: [
                "NoMask",
                "GlossinessMask",
                "NormalStrengthMask",
                "ClearcoatAmountMask",
                "ClearcoatRoughnessAmountMask",
                "ClearcoatNormalStrengthMask",
                "HeightAmountMask",
                "OcclusionAmountMask",
                "ThicknessFactorMask",
                "TransmissionFactorMask"
            ]
        }
        Enum {
            name: "VertexColorMaskFlags"
            alias: "VertexColorMask"
            isFlag: true
            values: [
                "NoMask",
                "GlossinessMask",
                "NormalStrengthMask",
                "ClearcoatAmountMask",
                "ClearcoatRoughnessAmountMask",
                "ClearcoatNormalStrengthMask",
                "HeightAmountMask",
                "OcclusionAmountMask",
                "ThicknessFactorMask",
                "TransmissionFactorMask"
            ]
        }
        Property {
            name: "lighting"
            type: "Lighting"
            read: "lighting"
            write: "setLighting"
            notify: "lightingChanged"
            index: 0
        }
        Property {
            name: "blendMode"
            type: "BlendMode"
            read: "blendMode"
            write: "setBlendMode"
            notify: "blendModeChanged"
            index: 1
        }
        Property {
            name: "albedoColor"
            type: "QColor"
            read: "albedoColor"
            write: "setAlbedoColor"
            notify: "albedoColorChanged"
            index: 2
        }
        Property {
            name: "albedoMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "albedoMap"
            write: "setAlbedoMap"
            notify: "albedoMapChanged"
            index: 3
        }
        Property {
            name: "albedoSingleChannelEnabled"
            revision: 1544
            type: "bool"
            read: "albedoSingleChannelEnabled"
            write: "setAlbedoSingleChannelEnabled"
            notify: "albedoSingleChannelEnabledChanged"
            index: 4
        }
        Property {
            name: "albedoChannel"
            revision: 1544
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "albedoChannel"
            write: "setAlbedoChannel"
            notify: "albedoChannelChanged"
            index: 5
        }
        Property {
            name: "specularColor"
            type: "QColor"
            read: "specularColor"
            write: "setSpecularColor"
            notify: "specularColorChanged"
            index: 6
        }
        Property {
            name: "specularMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "specularMap"
            write: "setSpecularMap"
            notify: "specularMapChanged"
            index: 7
        }
        Property {
            name: "specularSingleChannelEnabled"
            revision: 1544
            type: "bool"
            read: "specularSingleChannelEnabled"
            write: "setSpecularSingleChannelEnabled"
            notify: "specularSingleChannelEnabledChanged"
            index: 8
        }
        Property {
            name: "specularChannel"
            revision: 1544
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "specularChannel"
            write: "setSpecularChannel"
            notify: "specularChannelChanged"
            index: 9
        }
        Property {
            name: "glossiness"
            type: "float"
            read: "glossiness"
            write: "setGlossiness"
            notify: "glossinessChanged"
            index: 10
        }
        Property {
            name: "glossinessMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "glossinessMap"
            write: "setGlossinessMap"
            notify: "glossinessMapChanged"
            index: 11
        }
        Property {
            name: "glossinessChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "glossinessChannel"
            write: "setGlossinessChannel"
            notify: "glossinessChannelChanged"
            index: 12
        }
        Property {
            name: "emissiveFactor"
            type: "QVector3D"
            read: "emissiveFactor"
            write: "setEmissiveFactor"
            notify: "emissiveFactorChanged"
            index: 13
        }
        Property {
            name: "emissiveMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "emissiveMap"
            write: "setEmissiveMap"
            notify: "emissiveMapChanged"
            index: 14
        }
        Property {
            name: "emissiveSingleChannelEnabled"
            revision: 1544
            type: "bool"
            read: "emissiveSingleChannelEnabled"
            write: "setEmissiveSingleChannelEnabled"
            notify: "emissiveSingleChannelEnabledChanged"
            index: 15
        }
        Property {
            name: "emissiveChannel"
            revision: 1544
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "emissiveChannel"
            write: "setEmissiveChannel"
            notify: "emissiveChannelChanged"
            index: 16
        }
        Property {
            name: "invertOpacityMapValue"
            revision: 1544
            type: "bool"
            read: "invertOpacityMapValue"
            write: "setInvertOpacityMapValue"
            notify: "invertOpacityMapValueChanged"
            index: 17
        }
        Property {
            name: "opacity"
            type: "float"
            read: "opacity"
            write: "setOpacity"
            notify: "opacityChanged"
            index: 18
        }
        Property {
            name: "opacityMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "opacityMap"
            write: "setOpacityMap"
            notify: "opacityMapChanged"
            index: 19
        }
        Property {
            name: "opacityChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "opacityChannel"
            write: "setOpacityChannel"
            notify: "opacityChannelChanged"
            index: 20
        }
        Property {
            name: "normalMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "normalMap"
            write: "setNormalMap"
            notify: "normalMapChanged"
            index: 21
        }
        Property {
            name: "normalStrength"
            type: "float"
            read: "normalStrength"
            write: "setNormalStrength"
            notify: "normalStrengthChanged"
            index: 22
        }
        Property {
            name: "occlusionMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "occlusionMap"
            write: "setOcclusionMap"
            notify: "occlusionMapChanged"
            index: 23
        }
        Property {
            name: "occlusionChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "occlusionChannel"
            write: "setOcclusionChannel"
            notify: "occlusionChannelChanged"
            index: 24
        }
        Property {
            name: "occlusionAmount"
            type: "float"
            read: "occlusionAmount"
            write: "setOcclusionAmount"
            notify: "occlusionAmountChanged"
            index: 25
        }
        Property {
            name: "alphaMode"
            type: "AlphaMode"
            read: "alphaMode"
            write: "setAlphaMode"
            notify: "alphaModeChanged"
            index: 26
        }
        Property {
            name: "alphaCutoff"
            type: "float"
            read: "alphaCutoff"
            write: "setAlphaCutoff"
            notify: "alphaCutoffChanged"
            index: 27
        }
        Property {
            name: "pointSize"
            type: "float"
            read: "pointSize"
            write: "setPointSize"
            notify: "pointSizeChanged"
            index: 28
        }
        Property {
            name: "lineWidth"
            type: "float"
            read: "lineWidth"
            write: "setLineWidth"
            notify: "lineWidthChanged"
            index: 29
        }
        Property {
            name: "heightMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "heightMap"
            write: "setHeightMap"
            notify: "heightMapChanged"
            index: 30
        }
        Property {
            name: "heightChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "heightChannel"
            write: "setHeightChannel"
            notify: "heightChannelChanged"
            index: 31
        }
        Property {
            name: "heightAmount"
            type: "float"
            read: "heightAmount"
            write: "setHeightAmount"
            notify: "heightAmountChanged"
            index: 32
        }
        Property {
            name: "minHeightMapSamples"
            type: "int"
            read: "minHeightMapSamples"
            write: "setMinHeightMapSamples"
            notify: "minHeightMapSamplesChanged"
            index: 33
        }
        Property {
            name: "maxHeightMapSamples"
            type: "int"
            read: "maxHeightMapSamples"
            write: "setMaxHeightMapSamples"
            notify: "maxHeightMapSamplesChanged"
            index: 34
        }
        Property {
            name: "clearcoatAmount"
            type: "float"
            read: "clearcoatAmount"
            write: "setClearcoatAmount"
            notify: "clearcoatAmountChanged"
            index: 35
        }
        Property {
            name: "clearcoatMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "clearcoatMap"
            write: "setClearcoatMap"
            notify: "clearcoatMapChanged"
            index: 36
        }
        Property {
            name: "clearcoatChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "clearcoatChannel"
            write: "setClearcoatChannel"
            notify: "clearcoatChannelChanged"
            index: 37
        }
        Property {
            name: "clearcoatRoughnessAmount"
            type: "float"
            read: "clearcoatRoughnessAmount"
            write: "setClearcoatRoughnessAmount"
            notify: "clearcoatRoughnessAmountChanged"
            index: 38
        }
        Property {
            name: "clearcoatRoughnessChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "clearcoatRoughnessChannel"
            write: "setClearcoatRoughnessChannel"
            notify: "clearcoatRoughnessChannelChanged"
            index: 39
        }
        Property {
            name: "clearcoatRoughnessMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "clearcoatRoughnessMap"
            write: "setClearcoatRoughnessMap"
            notify: "clearcoatRoughnessMapChanged"
            index: 40
        }
        Property {
            name: "clearcoatNormalMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "clearcoatNormalMap"
            write: "setClearcoatNormalMap"
            notify: "clearcoatNormalMapChanged"
            index: 41
        }
        Property {
            name: "clearcoatNormalStrength"
            revision: 1544
            type: "float"
            read: "clearcoatNormalStrength"
            write: "setClearcoatNormalStrength"
            notify: "clearcoatNormalStrengthChanged"
            index: 42
        }
        Property {
            name: "transmissionFactor"
            type: "float"
            read: "transmissionFactor"
            write: "setTransmissionFactor"
            notify: "transmissionFactorChanged"
            index: 43
        }
        Property {
            name: "transmissionMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "transmissionMap"
            write: "setTransmissionMap"
            notify: "transmissionMapChanged"
            index: 44
        }
        Property {
            name: "transmissionChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "transmissionChannel"
            write: "setTransmissionChannel"
            notify: "transmissionChannelChanged"
            index: 45
        }
        Property {
            name: "thicknessFactor"
            type: "float"
            read: "thicknessFactor"
            write: "setThicknessFactor"
            notify: "thicknessFactorChanged"
            index: 46
        }
        Property {
            name: "thicknessMap"
            type: "QQuick3DTexture"
            isPointer: true
            read: "thicknessMap"
            write: "setThicknessMap"
            notify: "thicknessMapChanged"
            index: 47
        }
        Property {
            name: "thicknessChannel"
            type: "QQuick3DMaterial::TextureChannelMapping"
            read: "thicknessChannel"
            write: "setThicknessChannel"
            notify: "thicknessChannelChanged"
            index: 48
        }
        Property {
            name: "attenuationDistance"
            type: "float"
            read: "attenuationDistance"
            write: "setAttenuationDistance"
            notify: "attenuationDistanceChanged"
            index: 49
        }
        Property {
            name: "attenuationColor"
            type: "QColor"
            read: "attenuationColor"
            write: "setAttenuationColor"
            notify: "attenuationColorChanged"
            index: 50
        }
        Property {
            name: "vertexColorsEnabled"
            revision: 1541
            type: "bool"
            read: "vertexColorsEnabled"
            write: "setVertexColorsEnabled"
            notify: "vertexColorsEnabledChanged"
            index: 51
        }
        Property {
            name: "fresnelScaleBiasEnabled"
            revision: 1544
            type: "bool"
            read: "fresnelScaleBiasEnabled"
            write: "setFresnelScaleBiasEnabled"
            notify: "fresnelScaleBiasEnabledChanged"
            index: 52
        }
        Property {
            name: "fresnelScale"
            revision: 1544
            type: "float"
            read: "fresnelScale"
            write: "setFresnelScale"
            notify: "fresnelScaleChanged"
            index: 53
        }
        Property {
            name: "fresnelBias"
            revision: 1544
            type: "float"
            read: "fresnelBias"
            write: "setFresnelBias"
            notify: "fresnelBiasChanged"
            index: 54
        }
        Property {
            name: "fresnelPower"
            revision: 1544
            type: "float"
            read: "fresnelPower"
            write: "setFresnelPower"
            notify: "fresnelPowerChanged"
            index: 55
        }
        Property {
            name: "clearcoatFresnelScaleBiasEnabled"
            revision: 1544
            type: "bool"
            read: "clearcoatFresnelScaleBiasEnabled"
            write: "setClearcoatFresnelScaleBiasEnabled"
            notify: "clearcoatFresnelScaleBiasEnabledChanged"
            index: 56
        }
        Property {
            name: "clearcoatFresnelScale"
            revision: 1544
            type: "float"
            read: "clearcoatFresnelScale"
            write: "setClearcoatFresnelScale"
            notify: "clearcoatFresnelScaleChanged"
            index: 57
        }
        Property {
            name: "clearcoatFresnelBias"
            revision: 1544
            type: "float"
            read: "clearcoatFresnelBias"
            write: "setClearcoatFresnelBias"
            notify: "clearcoatFresnelBiasChanged"
            index: 58
        }
        Property {
            name: "clearcoatFresnelPower"
            revision: 1544
            type: "float"
            read: "clearcoatFresnelPower"
            write: "setClearcoatFresnelPower"
            notify: "clearcoatFresnelPowerChanged"
            index: 59
        }
        Property {
            name: "vertexColorsMaskEnabled"
            revision: 1544
            type: "bool"
            read: "vertexColorsMaskEnabled"
            write: "setVertexColorsMaskEnabled"
            notify: "vertexColorsMaskEnabledChanged"
            index: 60
        }
        Property {
            name: "vertexColorRedMask"
            revision: 1544
            type: "VertexColorMaskFlags"
            read: "vertexColorRedMask"
            write: "setVertexColorRedMask"
            notify: "vertexColorRedMaskChanged"
            index: 61
        }
        Property {
            name: "vertexColorGreenMask"
            revision: 1544
            type: "VertexColorMaskFlags"
            read: "vertexColorGreenMask"
            write: "setVertexColorGreenMask"
            notify: "vertexColorGreenMaskChanged"
            index: 62
        }
        Property {
            name: "vertexColorBlueMask"
            revision: 1544
            type: "VertexColorMaskFlags"
            read: "vertexColorBlueMask"
            write: "setVertexColorBlueMask"
            notify: "vertexColorBlueMaskChanged"
            index: 63
        }
        Property {
            name: "vertexColorAlphaMask"
            revision: 1544
            type: "VertexColorMaskFlags"
            read: "vertexColorAlphaMask"
            write: "setVertexColorAlphaMask"
            notify: "vertexColorAlphaMaskChanged"
            index: 64
        }
        Signal { name: "lightingChanged" }
        Signal { name: "blendModeChanged" }
        Signal { name: "albedoColorChanged" }
        Signal { name: "albedoMapChanged" }
        Signal { name: "albedoSingleChannelEnabledChanged"; revision: 1544 }
        Signal { name: "albedoChannelChanged"; revision: 1544 }
        Signal { name: "specularSingleChannelEnabledChanged"; revision: 1544 }
        Signal { name: "specularChannelChanged"; revision: 1544 }
        Signal { name: "emissiveSingleChannelEnabledChanged"; revision: 1544 }
        Signal { name: "emissiveChannelChanged"; revision: 1544 }
        Signal { name: "emissiveMapChanged" }
        Signal { name: "emissiveFactorChanged" }
        Signal { name: "glossinessChanged" }
        Signal { name: "glossinessMapChanged" }
        Signal { name: "invertOpacityMapValueChanged"; revision: 1544 }
        Signal { name: "opacityChanged" }
        Signal { name: "opacityMapChanged" }
        Signal { name: "normalMapChanged" }
        Signal { name: "specularColorChanged" }
        Signal { name: "specularMapChanged" }
        Signal { name: "normalStrengthChanged" }
        Signal { name: "occlusionMapChanged" }
        Signal { name: "occlusionAmountChanged" }
        Signal { name: "alphaModeChanged" }
        Signal { name: "alphaCutoffChanged" }
        Signal { name: "glossinessChannelChanged" }
        Signal { name: "opacityChannelChanged" }
        Signal { name: "occlusionChannelChanged" }
        Signal { name: "pointSizeChanged" }
        Signal { name: "lineWidthChanged" }
        Signal { name: "heightMapChanged" }
        Signal { name: "heightChannelChanged" }
        Signal { name: "heightAmountChanged" }
        Signal { name: "minHeightMapSamplesChanged" }
        Signal { name: "maxHeightMapSamplesChanged" }
        Signal { name: "clearcoatAmountChanged" }
        Signal { name: "clearcoatMapChanged" }
        Signal { name: "clearcoatChannelChanged" }
        Signal { name: "clearcoatRoughnessAmountChanged" }
        Signal { name: "clearcoatRoughnessChannelChanged" }
        Signal { name: "clearcoatRoughnessMapChanged" }
        Signal { name: "clearcoatNormalMapChanged" }
        Signal { name: "clearcoatNormalStrengthChanged" }
        Signal { name: "transmissionFactorChanged" }
        Signal { name: "transmissionMapChanged" }
        Signal { name: "transmissionChannelChanged" }
        Signal { name: "thicknessFactorChanged" }
        Signal { name: "thicknessMapChanged" }
        Signal { name: "thicknessChannelChanged" }
        Signal { name: "attenuationDistanceChanged" }
        Signal { name: "attenuationColorChanged" }
        Signal {
            name: "vertexColorsEnabledChanged"
            revision: 1541
            Parameter { name: "vertexColorsEnabled"; type: "bool" }
        }
        Signal {
            name: "fresnelScaleBiasEnabledChanged"
            revision: 1544
            Parameter { name: "fresnelScaleBiasEnabled"; type: "bool" }
        }
        Signal {
            name: "fresnelScaleChanged"
            revision: 1544
            Parameter { name: "fresnelScale"; type: "float" }
        }
        Signal {
            name: "fresnelBiasChanged"
            revision: 1544
            Parameter { name: "fresnelBias"; type: "float" }
        }
        Signal {
            name: "fresnelPowerChanged"
            revision: 1544
            Parameter { name: "fresnelPower"; type: "float" }
        }
        Signal {
            name: "clearcoatFresnelScaleBiasEnabledChanged"
            revision: 1544
            Parameter { name: "clearcoatFresnelScaleBiasEnabled"; type: "bool" }
        }
        Signal {
            name: "clearcoatFresnelScaleChanged"
            revision: 1544
            Parameter { name: "clearcoatFresnelScale"; type: "float" }
        }
        Signal {
            name: "clearcoatFresnelBiasChanged"
            revision: 1544
            Parameter { name: "clearcoatFresnelBias"; type: "float" }
        }
        Signal {
            name: "clearcoatFresnelPowerChanged"
            revision: 1544
            Parameter { name: "clearcoatFresnelPower"; type: "float" }
        }
        Signal { name: "vertexColorsMaskEnabledChanged"; revision: 1544 }
        Signal { name: "vertexColorRedMaskChanged"; revision: 1544 }
        Signal { name: "vertexColorGreenMaskChanged"; revision: 1544 }
        Signal { name: "vertexColorBlueMaskChanged"; revision: 1544 }
        Signal { name: "vertexColorAlphaMaskChanged"; revision: 1544 }
        Method {
            name: "setLighting"
            Parameter { name: "lighting"; type: "QQuick3DSpecularGlossyMaterial::Lighting" }
        }
        Method {
            name: "setBlendMode"
            Parameter { name: "blendMode"; type: "QQuick3DSpecularGlossyMaterial::BlendMode" }
        }
        Method {
            name: "setAlbedoColor"
            Parameter { name: "albedo"; type: "QColor" }
        }
        Method {
            name: "setAlbedoMap"
            Parameter { name: "albedoMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setAlbedoSingleChannelEnabled"
            revision: 1544
            Parameter { name: "albedoSingleChannelEnabled"; type: "bool" }
        }
        Method {
            name: "setAlbedoChannel"
            revision: 1544
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setSpecularSingleChannelEnabled"
            revision: 1544
            Parameter { name: "specularSingleChannelEnabled"; type: "bool" }
        }
        Method {
            name: "setSpecularChannel"
            revision: 1544
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setEmissiveSingleChannelEnabled"
            revision: 1544
            Parameter { name: "emissiveSingleChannelEnabled"; type: "bool" }
        }
        Method {
            name: "setEmissiveChannel"
            revision: 1544
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setEmissiveMap"
            Parameter { name: "emissiveMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setEmissiveFactor"
            Parameter { name: "emissiveFactor"; type: "QVector3D" }
        }
        Method {
            name: "setGlossiness"
            Parameter { name: "glossiness"; type: "float" }
        }
        Method {
            name: "setGlossinessMap"
            Parameter { name: "glossinessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setInvertOpacityMapValue"
            revision: 1544
            Parameter { name: "invertOpacityMapValue"; type: "bool" }
        }
        Method {
            name: "setOpacity"
            Parameter { name: "opacity"; type: "float" }
        }
        Method {
            name: "setOpacityMap"
            Parameter { name: "opacityMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setNormalMap"
            Parameter { name: "normalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setSpecularColor"
            Parameter { name: "specular"; type: "QColor" }
        }
        Method {
            name: "setSpecularMap"
            Parameter { name: "specularMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setNormalStrength"
            Parameter { name: "normalStrength"; type: "float" }
        }
        Method {
            name: "setOcclusionMap"
            Parameter { name: "occlusionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setOcclusionAmount"
            Parameter { name: "occlusionAmount"; type: "float" }
        }
        Method {
            name: "setAlphaMode"
            Parameter { name: "alphaMode"; type: "QQuick3DSpecularGlossyMaterial::AlphaMode" }
        }
        Method {
            name: "setAlphaCutoff"
            Parameter { name: "alphaCutoff"; type: "float" }
        }
        Method {
            name: "setGlossinessChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setOpacityChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setOcclusionChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setPointSize"
            Parameter { name: "size"; type: "float" }
        }
        Method {
            name: "setLineWidth"
            Parameter { name: "width"; type: "float" }
        }
        Method {
            name: "setHeightMap"
            Parameter { name: "heightMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setHeightChannel"
            Parameter { name: "channel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setHeightAmount"
            Parameter { name: "heightAmount"; type: "float" }
        }
        Method {
            name: "setMinHeightMapSamples"
            Parameter { name: "samples"; type: "int" }
        }
        Method {
            name: "setMaxHeightMapSamples"
            Parameter { name: "samples"; type: "int" }
        }
        Method {
            name: "setClearcoatAmount"
            Parameter { name: "newClearcoatAmount"; type: "float" }
        }
        Method {
            name: "setClearcoatMap"
            Parameter { name: "newClearcoatMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setClearcoatChannel"
            Parameter { name: "newClearcoatChannel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setClearcoatRoughnessAmount"
            Parameter { name: "newClearcoatRoughnessAmount"; type: "float" }
        }
        Method {
            name: "setClearcoatRoughnessChannel"
            Parameter {
                name: "newClearcoatRoughnessChannel"
                type: "QQuick3DMaterial::TextureChannelMapping"
            }
        }
        Method {
            name: "setClearcoatRoughnessMap"
            Parameter { name: "newClearcoatRoughnessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setClearcoatNormalMap"
            Parameter { name: "newClearcoatNormalMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setClearcoatNormalStrength"
            revision: 1544
            Parameter { name: "newClearcoatNormalStrength"; type: "float" }
        }
        Method {
            name: "setTransmissionFactor"
            Parameter { name: "newTransmissionFactor"; type: "float" }
        }
        Method {
            name: "setTransmissionMap"
            Parameter { name: "newTransmissionMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setTransmissionChannel"
            Parameter { name: "newTransmissionChannel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setThicknessFactor"
            Parameter { name: "newThicknessFactor"; type: "float" }
        }
        Method {
            name: "setThicknessMap"
            Parameter { name: "newThicknessMap"; type: "QQuick3DTexture"; isPointer: true }
        }
        Method {
            name: "setThicknessChannel"
            Parameter { name: "newThicknessChannel"; type: "QQuick3DMaterial::TextureChannelMapping" }
        }
        Method {
            name: "setAttenuationDistance"
            Parameter { name: "newAttenuationDistance"; type: "float" }
        }
        Method {
            name: "setAttenuationColor"
            Parameter { name: "newAttenuationColor"; type: "QColor" }
        }
        Method {
            name: "setFresnelScaleBiasEnabled"
            revision: 1544
            Parameter { name: "fresnelScaleBias"; type: "bool" }
        }
        Method {
            name: "setFresnelScale"
            revision: 1544
            Parameter { name: "fresnelScale"; type: "float" }
        }
        Method {
            name: "setFresnelBias"
            revision: 1544
            Parameter { name: "fresnelBias"; type: "float" }
        }
        Method {
            name: "setFresnelPower"
            revision: 1544
            Parameter { name: "fresnelPower"; type: "float" }
        }
        Method {
            name: "setClearcoatFresnelScaleBiasEnabled"
            revision: 1544
            Parameter { name: "clearcoatFresnelScaleBias"; type: "bool" }
        }
        Method {
            name: "setClearcoatFresnelScale"
            revision: 1544
            Parameter { name: "clearcoatFresnelScale"; type: "float" }
        }
        Method {
            name: "setClearcoatFresnelBias"
            revision: 1544
            Parameter { name: "clearcoatFresnelBias"; type: "float" }
        }
        Method {
            name: "setClearcoatFresnelPower"
            revision: 1544
            Parameter { name: "clearcoatFresnelPower"; type: "float" }
        }
        Method {
            name: "setVertexColorsEnabled"
            revision: 1541
            Parameter { name: "vertexColorsEnabled"; type: "bool" }
        }
        Method {
            name: "setVertexColorsMaskEnabled"
            revision: 1544
            Parameter { name: "vertexColorsMaskEnabled"; type: "bool" }
        }
        Method {
            name: "setVertexColorRedMask"
            revision: 1544
            Parameter { name: "vertexColorRedMask"; type: "VertexColorMaskFlags" }
        }
        Method {
            name: "setVertexColorGreenMask"
            revision: 1544
            Parameter { name: "vertexColorGreenMask"; type: "VertexColorMaskFlags" }
        }
        Method {
            name: "setVertexColorBlueMask"
            revision: 1544
            Parameter { name: "vertexColorBlueMask"; type: "VertexColorMaskFlags" }
        }
        Method {
            name: "setVertexColorAlphaMask"
            revision: 1544
            Parameter { name: "vertexColorAlphaMask"; type: "VertexColorMaskFlags" }
        }
    }
    Component {
        file: "private/qquick3dspotlight_p.h"
        name: "QQuick3DSpotLight"
        accessSemantics: "reference"
        prototype: "QQuick3DAbstractLight"
        exports: [
            "QtQuick3D/SpotLight 6.0",
            "QtQuick3D/SpotLight 6.8",
            "QtQuick3D/SpotLight 6.9"
        ]
        exportMetaObjectRevisions: [1536, 1544, 1545]
        Property {
            name: "constantFade"
            type: "float"
            read: "constantFade"
            write: "setConstantFade"
            notify: "constantFadeChanged"
            index: 0
        }
        Property {
            name: "linearFade"
            type: "float"
            read: "linearFade"
            write: "setLinearFade"
            notify: "linearFadeChanged"
            index: 1
        }
        Property {
            name: "quadraticFade"
            type: "float"
            read: "quadraticFade"
            write: "setQuadraticFade"
            notify: "quadraticFadeChanged"
            index: 2
        }
        Property {
            name: "coneAngle"
            type: "float"
            read: "coneAngle"
            write: "setConeAngle"
            notify: "coneAngleChanged"
            index: 3
        }
        Property {
            name: "innerConeAngle"
            type: "float"
            read: "innerConeAngle"
            write: "setInnerConeAngle"
            notify: "innerConeAngleChanged"
            index: 4
        }
        Signal { name: "constantFadeChanged" }
        Signal { name: "linearFadeChanged" }
        Signal { name: "quadraticFadeChanged" }
        Signal { name: "coneAngleChanged" }
        Signal { name: "innerConeAngleChanged" }
        Method {
            name: "setConstantFade"
            Parameter { name: "constantFade"; type: "float" }
        }
        Method {
            name: "setLinearFade"
            Parameter { name: "linearFade"; type: "float" }
        }
        Method {
            name: "setQuadraticFade"
            Parameter { name: "quadraticFade"; type: "float" }
        }
        Method {
            name: "setConeAngle"
            Parameter { name: "coneAngle"; type: "float" }
        }
        Method {
            name: "setInnerConeAngle"
            Parameter { name: "innerConeAngle"; type: "float" }
        }
    }
    Component {
        file: "private/qquick3dtexture_p.h"
        name: "QQuick3DTexture"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: [
            "QtQuick3D/Texture 6.0",
            "QtQuick3D/Texture 6.2",
            "QtQuick3D/Texture 6.7"
        ]
        exportMetaObjectRevisions: [1536, 1538, 1543]
        Enum {
            name: "MappingMode"
            values: ["UV", "Environment", "LightProbe"]
        }
        Enum {
            name: "TilingMode"
            values: ["ClampToEdge", "MirroredRepeat", "Repeat"]
        }
        Enum {
            name: "Filter"
            values: ["None", "Nearest", "Linear"]
        }
        Property {
            name: "source"
            type: "QUrl"
            read: "source"
            write: "setSource"
            notify: "sourceChanged"
            index: 0
        }
        Property {
            name: "sourceItem"
            type: "QQuickItem"
            isPointer: true
            read: "sourceItem"
            write: "setSourceItem"
            notify: "sourceItemChanged"
            index: 1
        }
        Property {
            name: "textureData"
            type: "QQuick3DTextureData"
            isPointer: true
            read: "textureData"
            write: "setTextureData"
            notify: "textureDataChanged"
            index: 2
        }
        Property {
            name: "textureProvider"
            revision: 1543
            type: "QQuick3DRenderExtension"
            isPointer: true
            read: "textureProvider"
            write: "setTextureProvider"
            notify: "textureProviderChanged"
            index: 3
            isFinal: true
        }
        Property {
            name: "scaleU"
            type: "float"
            read: "scaleU"
            write: "setScaleU"
            notify: "scaleUChanged"
            index: 4
        }
        Property {
            name: "scaleV"
            type: "float"
            read: "scaleV"
            write: "setScaleV"
            notify: "scaleVChanged"
            index: 5
        }
        Property {
            name: "mappingMode"
            type: "MappingMode"
            read: "mappingMode"
            write: "setMappingMode"
            notify: "mappingModeChanged"
            index: 6
        }
        Property {
            name: "tilingModeHorizontal"
            type: "TilingMode"
            read: "horizontalTiling"
            write: "setHorizontalTiling"
            notify: "horizontalTilingChanged"
            index: 7
        }
        Property {
            name: "tilingModeVertical"
            type: "TilingMode"
            read: "verticalTiling"
            write: "setVerticalTiling"
            notify: "verticalTilingChanged"
            index: 8
        }
        Property {
            name: "tilingModeDepth"
            revision: 1543
            type: "TilingMode"
            read: "depthTiling"
            write: "setDepthTiling"
            notify: "depthTilingChanged"
            index: 9
        }
        Property {
            name: "rotationUV"
            type: "float"
            read: "rotationUV"
            write: "setRotationUV"
            notify: "rotationUVChanged"
            index: 10
        }
        Property {
            name: "positionU"
            type: "float"
            read: "positionU"
            write: "setPositionU"
            notify: "positionUChanged"
            index: 11
        }
        Property {
            name: "positionV"
            type: "float"
            read: "positionV"
            write: "setPositionV"
            notify: "positionVChanged"
            index: 12
        }
        Property {
            name: "pivotU"
            type: "float"
            read: "pivotU"
            write: "setPivotU"
            notify: "pivotUChanged"
            index: 13
        }
        Property {
            name: "pivotV"
            type: "float"
            read: "pivotV"
            write: "setPivotV"
            notify: "pivotVChanged"
            index: 14
        }
        Property {
            name: "flipU"
            type: "bool"
            read: "flipU"
            write: "setFlipU"
            notify: "flipUChanged"
            index: 15
        }
        Property {
            name: "flipV"
            type: "bool"
            read: "flipV"
            write: "setFlipV"
            notify: "flipVChanged"
            index: 16
        }
        Property {
            name: "indexUV"
            type: "int"
            read: "indexUV"
            write: "setIndexUV"
            notify: "indexUVChanged"
            index: 17
        }
        Property {
            name: "magFilter"
            type: "Filter"
            read: "magFilter"
            write: "setMagFilter"
            notify: "magFilterChanged"
            index: 18
        }
        Property {
            name: "minFilter"
            type: "Filter"
            read: "minFilter"
            write: "setMinFilter"
            notify: "minFilterChanged"
            index: 19
        }
        Property {
            name: "mipFilter"
            type: "Filter"
            read: "mipFilter"
            write: "setMipFilter"
            notify: "mipFilterChanged"
            index: 20
        }
        Property {
            name: "generateMipmaps"
            type: "bool"
            read: "generateMipmaps"
            write: "setGenerateMipmaps"
            notify: "generateMipmapsChanged"
            index: 21
        }
        Property {
            name: "autoOrientation"
            revision: 1538
            type: "bool"
            read: "autoOrientation"
            write: "setAutoOrientation"
            notify: "autoOrientationChanged"
            index: 22
        }
        Signal { name: "sourceChanged" }
        Signal { name: "sourceItemChanged" }
        Signal { name: "scaleUChanged" }
        Signal { name: "scaleVChanged" }
        Signal { name: "mappingModeChanged" }
        Signal { name: "horizontalTilingChanged" }
        Signal { name: "verticalTilingChanged" }
        Signal { name: "depthTilingChanged"; revision: 1543 }
        Signal { name: "rotationUVChanged" }
        Signal { name: "positionUChanged" }
        Signal { name: "positionVChanged" }
        Signal { name: "pivotUChanged" }
        Signal { name: "pivotVChanged" }
        Signal { name: "flipUChanged" }
        Signal { name: "flipVChanged" }
        Signal { name: "indexUVChanged" }
        Signal { name: "magFilterChanged" }
        Signal { name: "minFilterChanged" }
        Signal { name: "mipFilterChanged" }
        Signal { name: "textureDataChanged" }
        Signal { name: "generateMipmapsChanged" }
        Signal { name: "autoOrientationChanged" }
        Signal { name: "textureProviderChanged"; revision: 1543 }
        Method {
            name: "setSource"
            Parameter { name: "source"; type: "QUrl" }
        }
        Method {
            name: "setSourceItem"
            Parameter { name: "sourceItem"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "setScaleU"
            Parameter { name: "scaleU"; type: "float" }
        }
        Method {
            name: "setScaleV"
            Parameter { name: "scaleV"; type: "float" }
        }
        Method {
            name: "setMappingMode"
            Parameter { name: "mappingMode"; type: "QQuick3DTexture::MappingMode" }
        }
        Method {
            name: "setHorizontalTiling"
            Parameter { name: "tilingModeHorizontal"; type: "QQuick3DTexture::TilingMode" }
        }
        Method {
            name: "setVerticalTiling"
            Parameter { name: "tilingModeVertical"; type: "QQuick3DTexture::TilingMode" }
        }
        Method {
            name: "setDepthTiling"
            revision: 1543
            Parameter { name: "tilingModeDepth"; type: "QQuick3DTexture::TilingMode" }
        }
        Method {
            name: "setRotationUV"
            Parameter { name: "rotationUV"; type: "float" }
        }
        Method {
            name: "setPositionU"
            Parameter { name: "positionU"; type: "float" }
        }
        Method {
            name: "setPositionV"
            Parameter { name: "positionV"; type: "float" }
        }
        Method {
            name: "setPivotU"
            Parameter { name: "pivotU"; type: "float" }
        }
        Method {
            name: "setPivotV"
            Parameter { name: "pivotV"; type: "float" }
        }
        Method {
            name: "setFlipU"
            Parameter { name: "flipU"; type: "bool" }
        }
        Method {
            name: "setFlipV"
            Parameter { name: "flipV"; type: "bool" }
        }
        Method {
            name: "setIndexUV"
            Parameter { name: "indexUV"; type: "int" }
        }
        Method {
            name: "setMagFilter"
            Parameter { name: "magFilter"; type: "QQuick3DTexture::Filter" }
        }
        Method {
            name: "setMinFilter"
            Parameter { name: "minFilter"; type: "QQuick3DTexture::Filter" }
        }
        Method {
            name: "setMipFilter"
            Parameter { name: "mipFilter"; type: "QQuick3DTexture::Filter" }
        }
        Method {
            name: "setTextureData"
            Parameter { name: "textureData"; type: "QQuick3DTextureData"; isPointer: true }
        }
        Method {
            name: "setGenerateMipmaps"
            Parameter { name: "generateMipmaps"; type: "bool" }
        }
        Method {
            name: "setAutoOrientation"
            Parameter { name: "autoOrientation"; type: "bool" }
        }
        Method {
            name: "sourceItemDestroyed"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
    }
    Component {
        file: "qquick3dtexturedata.h"
        name: "QQuick3DTextureData"
        accessSemantics: "reference"
        defaultProperty: "data"
        prototype: "QQuick3DObject"
        exports: ["QtQuick3D/TextureData 6.0"]
        isCreatable: false
        exportMetaObjectRevisions: [1536]
        Enum {
            name: "Format"
            values: [
                "None",
                "RGBA8",
                "RGBA16F",
                "RGBA32F",
                "RGBE8",
                "R8",
                "R16",
                "R16F",
                "R32F",
                "BC1",
                "BC2",
                "BC3",
                "BC4",
                "BC5",
                "BC6H",
                "BC7",
                "DXT1_RGBA",
                "DXT1_RGB",
                "DXT3_RGBA",
                "DXT5_RGBA",
                "ETC2_RGB8",
                "ETC2_RGB8A1",
                "ETC2_RGBA8",
                "ASTC_4x4",
                "ASTC_5x4",
                "ASTC_5x5",
                "ASTC_6x5",
                "ASTC_6x6",
                "ASTC_8x5",
                "ASTC_8x6",
                "ASTC_8x8",
                "ASTC_10x5",
                "ASTC_10x6",
                "ASTC_10x8",
                "ASTC_10x10",
                "ASTC_12x10",
                "ASTC_12x12"
            ]
        }
        Signal { name: "textureDataNodeDirty" }
    }
    Component {
        file: "private/qquick3dviewport_p.h"
        name: "QQuick3DViewport"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtQuick3D/View3D 6.0",
            "QtQuick3D/View3D 6.2",
            "QtQuick3D/View3D 6.3",
            "QtQuick3D/View3D 6.4",
            "QtQuick3D/View3D 6.6",
            "QtQuick3D/View3D 6.7",
            "QtQuick3D/View3D 6.8"
        ]
        exportMetaObjectRevisions: [1536, 1538, 1539, 1540, 1542, 1543, 1544]
        Enum {
            name: "RenderMode"
            values: ["Offscreen", "Underlay", "Overlay", "Inline"]
        }
        Property {
            name: "data"
            type: "QObject"
            isList: true
            read: "data"
            index: 0
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "camera"
            type: "QQuick3DCamera"
            isPointer: true
            read: "camera"
            write: "setCamera"
            notify: "cameraChanged"
            index: 1
            isFinal: true
        }
        Property {
            name: "environment"
            type: "QQuick3DSceneEnvironment"
            isPointer: true
            read: "environment"
            write: "setEnvironment"
            notify: "environmentChanged"
            index: 2
            isFinal: true
        }
        Property {
            name: "scene"
            type: "QQuick3DNode"
            isPointer: true
            read: "scene"
            notify: "sceneChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "importScene"
            type: "QQuick3DNode"
            isPointer: true
            read: "importScene"
            write: "setImportScene"
            notify: "importSceneChanged"
            index: 4
            isFinal: true
        }
        Property {
            name: "renderMode"
            type: "RenderMode"
            read: "renderMode"
            write: "setRenderMode"
            notify: "renderModeChanged"
            index: 5
            isFinal: true
        }
        Property {
            name: "renderFormat"
            revision: 1540
            type: "QQuickShaderEffectSource::Format"
            read: "renderFormat"
            write: "setRenderFormat"
            notify: "renderFormatChanged"
            index: 6
            isFinal: true
        }
        Property {
            name: "renderStats"
            type: "QQuick3DRenderStats"
            isPointer: true
            read: "renderStats"
            index: 7
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "extensions"
            revision: 1542
            type: "QQuick3DObject"
            isList: true
            read: "extensions"
            index: 8
            isReadonly: true
            isFinal: true
        }
        Property {
            name: "explicitTextureWidth"
            revision: 1543
            type: "int"
            read: "explicitTextureWidth"
            write: "setExplicitTextureWidth"
            notify: "explicitTextureWidthChanged"
            index: 9
            isFinal: true
        }
        Property {
            name: "explicitTextureHeight"
            revision: 1543
            type: "int"
            read: "explicitTextureHeight"
            write: "setExplicitTextureHeight"
            notify: "explicitTextureHeightChanged"
            index: 10
            isFinal: true
        }
        Property {
            name: "effectiveTextureSize"
            revision: 1543
            type: "QSize"
            read: "effectiveTextureSize"
            notify: "effectiveTextureSizeChanged"
            index: 11
            isReadonly: true
            isFinal: true
        }
        Signal { name: "cameraChanged" }
        Signal { name: "environmentChanged" }
        Signal { name: "sceneChanged" }
        Signal { name: "importSceneChanged" }
        Signal { name: "renderModeChanged" }
        Signal { name: "renderFormatChanged"; revision: 1540 }
        Signal { name: "explicitTextureWidthChanged"; revision: 1543 }
        Signal { name: "explicitTextureHeightChanged"; revision: 1543 }
        Signal { name: "effectiveTextureSizeChanged"; revision: 1543 }
        Method {
            name: "setCamera"
            Parameter { name: "camera"; type: "QQuick3DCamera"; isPointer: true }
        }
        Method {
            name: "setEnvironment"
            Parameter { name: "environment"; type: "QQuick3DSceneEnvironment"; isPointer: true }
        }
        Method {
            name: "setImportScene"
            Parameter { name: "inScene"; type: "QQuick3DNode"; isPointer: true }
        }
        Method {
            name: "setRenderMode"
            Parameter { name: "renderMode"; type: "QQuick3DViewport::RenderMode" }
        }
        Method {
            name: "setRenderFormat"
            revision: 1540
            Parameter { name: "format"; type: "QQuickShaderEffectSource::Format" }
        }
        Method {
            name: "setExplicitTextureWidth"
            revision: 1543
            Parameter { name: "width"; type: "int" }
        }
        Method {
            name: "setExplicitTextureHeight"
            revision: 1543
            Parameter { name: "height"; type: "int" }
        }
        Method { name: "cleanupDirectRenderer" }
        Method {
            name: "setGlobalPickingEnabled"
            Parameter { name: "isEnabled"; type: "bool" }
        }
        Method { name: "invalidateSceneGraph" }
        Method { name: "updateInputProcessing" }
        Method { name: "onReleaseCachedResources" }
        Method {
            name: "mapFrom3DScene"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "scenePos"; type: "QVector3D" }
        }
        Method {
            name: "mapTo3DScene"
            type: "QVector3D"
            isMethodConstant: true
            Parameter { name: "viewPos"; type: "QVector3D" }
        }
        Method {
            name: "pick"
            type: "QQuick3DPickResult"
            isMethodConstant: true
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
        }
        Method {
            name: "pick"
            revision: 1544
            type: "QQuick3DPickResult"
            isMethodConstant: true
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
            Parameter { name: "model"; type: "QQuick3DModel"; isPointer: true }
        }
        Method {
            name: "pickSubset"
            revision: 1544
            type: "QQuick3DPickResult"
            isList: true
            isMethodConstant: true
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
            Parameter { name: "models"; type: "QJSValue" }
        }
        Method {
            name: "pickAll"
            revision: 1538
            type: "QQuick3DPickResult"
            isList: true
            isMethodConstant: true
            Parameter { name: "x"; type: "float" }
            Parameter { name: "y"; type: "float" }
        }
        Method {
            name: "rayPick"
            revision: 1538
            type: "QQuick3DPickResult"
            isMethodConstant: true
            Parameter { name: "origin"; type: "QVector3D" }
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Method {
            name: "rayPickAll"
            revision: 1538
            type: "QQuick3DPickResult"
            isList: true
            isMethodConstant: true
            Parameter { name: "origin"; type: "QVector3D" }
            Parameter { name: "direction"; type: "QVector3D" }
        }
        Method {
            name: "setTouchpoint"
            revision: 1544
            Parameter { name: "target"; type: "QQuickItem"; isPointer: true }
            Parameter { name: "position"; type: "QPointF" }
            Parameter { name: "pointId"; type: "int" }
            Parameter { name: "active"; type: "bool" }
        }
        Method { name: "bakeLightmap" }
        Method { name: "rebuildExtensionList"; revision: 1543 }
    }
}
