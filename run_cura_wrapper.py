#!/usr/bin/env python3
"""
Cura PyCharm包装脚本
这个脚本设置正确的环境并启动Cura
"""

import os
import sys
import subprocess

def main():
    # 设置工作目录
    cura_dir = "/Users/<USER>/PycharmProjects/CuraProject/Cura"
    os.chdir(cura_dir)
    
    # 设置环境变量
    env = os.environ.copy()
    
    # 添加本地Uranium到Python路径
    uranium_path = "/Users/<USER>/PycharmProjects/CuraProject/Uranium"
    if "PYTHONPATH" in env:
        env["PYTHONPATH"] = f"{uranium_path}:{env['PYTHONPATH']}"
    else:
        env["PYTHONPATH"] = uranium_path
    
    # 设置动态库路径（macOS）
    dyld_paths = [
        "/Users/<USER>/.conan2/p/cpyth346320b00af0c/p/lib",
        "/Users/<USER>/.conan2/p/pyarc4c699a4193985/p/lib", 
        "/Users/<USER>/.conan2/p/pysav5ca133687c881/p/lib",
        "/Users/<USER>/.conan2/p/pynes4b3b0a5d704e8/p/lib"
    ]
    
    if "DYLD_LIBRARY_PATH" in env:
        env["DYLD_LIBRARY_PATH"] = ":".join(dyld_paths) + ":" + env["DYLD_LIBRARY_PATH"]
    else:
        env["DYLD_LIBRARY_PATH"] = ":".join(dyld_paths)
    
    # 首先执行conanrun.sh来设置Conan环境
    conanrun_script = os.path.join(cura_dir, "build/generators/conanrun.sh")
    if os.path.exists(conanrun_script):
        # 读取conanrun.sh并解析环境变量
        try:
            result = subprocess.run(
                f"source {conanrun_script} && env",
                shell=True,
                capture_output=True,
                text=True,
                executable="/bin/bash"
            )
            if result.returncode == 0:
                # 解析环境变量
                for line in result.stdout.split('\n'):
                    if '=' in line and not line.startswith('_'):
                        key, value = line.split('=', 1)
                        env[key] = value
        except Exception as e:
            print(f"警告: 无法加载conanrun.sh环境: {e}")
    
    # 构建命令
    python_exe = "/Users/<USER>/.conan2/p/cpyth346320b00af0c/p/bin/python3.12"
    cmd = [python_exe, "cura_app.py", "--external-backend"] + sys.argv[1:]
    
    print(f"启动命令: {' '.join(cmd)}")
    print(f"工作目录: {cura_dir}")
    print(f"PYTHONPATH: {env.get('PYTHONPATH', 'Not set')}")
    
    # 启动Cura
    try:
        subprocess.run(cmd, env=env, cwd=cura_dir)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
