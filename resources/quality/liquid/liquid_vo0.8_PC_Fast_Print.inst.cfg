[general]
definition = liquid
name = Fast - Experimental
version = 4

[metadata]
is_experimental = True
material = generic_pc
quality_type = draft
setting_version = 25
type = quality
variant = VO 0.8
weight = -2

[values]
brim_width = 14
cool_fan_full_at_height = =layer_height_0 + 14 * layer_height
infill_before_walls = True
line_width = =machine_nozzle_size * 0.875
material_print_temperature = =default_material_print_temperature - 5
material_print_temperature_layer_0 = =material_print_temperature
material_standby_temperature = 100
raft_airgap = 0.5
raft_margin = 15
skin_overlap = 0
speed_layer_0 = =math.ceil(speed_print * 15 / 50)
speed_print = 50
speed_slowdown_layers = 15
speed_topbottom = =math.ceil(speed_print * 25 / 50)
speed_wall = =math.ceil(speed_print * 40 / 50)
speed_wall_0 = =math.ceil(speed_wall * 30 / 40)
support_line_width = =round(line_width * 0.6 / 0.7, 2)

