# Cura并行开发环境搭建指南

本文档记录了在macOS系统上搭建Cura、CuraEngine、Uranium并行开发环境的完整过程。

## 系统要求

### 已验证的系统环境
- macOS 11或更高版本
- Python 3.13.5 (要求 ≥3.12)
- Conan 2.17.0 (要求 ≥2.7.0 <3.0.0)
- CMake 3.27.9 (要求 ≥3.23)
- Ninja 1.11.1 (要求 ≥1.10)
- Xcode命令行工具

### 项目结构
```
CuraProject/
├── Cura/           # Cura主项目
├── CuraEngine/     # 切片引擎
└── Uranium/        # 基础框架库
```

## 搭建步骤

### 1. 配置Conan环境

#### 1.1 创建Python虚拟环境
```bash
cd /Users/<USER>/PycharmProjects/CuraProject
python3 -m venv cura_venv
source cura_venv/bin/activate
```

#### 1.2 安装指定版本Conan
```bash
pip install conan==2.7.0
```

#### 1.3 配置Conan
```bash
conan config install https://github.com/ultimaker/conan-config.git
conan profile detect --force
```

### 2. 设置Cura开发环境

#### 2.1 进入Cura目录并初始化环境
```bash
cd Cura
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv -c user.generator.virtual_python_env:dev_tools=True
```

**重要说明：**
- `-g VirtualPythonEnv`: 创建完整配置的Python虚拟环境
- `-g PyCharmRunEnv`: 生成PyCharm运行配置文件
- `-c user.generator.virtual_python_env:dev_tools=True`: 安装开发工具如pytest

#### 2.2 激活Cura虚拟环境
```bash
source build/generators/virtual_python_env.sh
```

#### 2.3 测试Cura运行
```bash
python cura_app.py
```

### 3. 配置Uranium并行开发

#### 3.1 设置Uranium为editable模式
```bash
cd ../Uranium
conan editable add . uranium/5.4.0@dev/testing
```

#### 3.2 重新配置Cura以使用本地Uranium
```bash
cd ../Cura
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv -c user.generator.virtual_python_env:dev_tools=True --require-override=uranium/5.4.0@dev/testing
```

### 4. 配置CuraEngine并行开发

#### 4.1 构建CuraEngine
```bash
cd ../CuraEngine
conan install . --build=missing --update
conan build .
```

#### 4.2 配置外部后端模式
在Cura中使用 `--external-backend` 参数或PyCharm的外部后端运行配置。

### 5. PyCharm IDE配置

#### 5.1 打开多项目工作空间
1. 在PyCharm中打开Cura项目
2. File > Open，选择Uranium目录，点击"Attach"
3. 重复步骤2添加CuraEngine项目

#### 5.2 配置运行环境
1. 点击"Add Configuration..."
2. 点击"Apply"应用Conan生成的配置文件
3. 配置文件位置：`Cura/build/generators/`

### 6. 验证安装

#### 6.1 测试Uranium链接
删除 `Uranium/UM/Application.py` 文件，如果Cura启动失败，说明链接成功。

#### 6.2 测试CuraEngine连接
1. 使用外部后端模式启动Cura
2. 开始切片
3. 运行 `curaengine connect 127.0.0.1:49674`

## 常见问题解决

### Conan相关问题
- 确保使用正确的Conan版本：`pip install --force-reinstall -v "conan==2.7.0"`
- 清理Conan缓存：`conan remove "*" -f`
- 不要使用sudo运行conan install

### 网络问题
如遇到网络连接问题，请暂停并联系管理员处理。

### PyCharm配置问题
- 确保点击了"Add Configuration..."和"Apply"
- 检查配置文件是否在 `build/generators/` 目录中生成

## 开发工作流

### 日常开发
1. 激活虚拟环境：`source cura_venv/bin/activate`
2. 进入Cura目录：`cd Cura`
3. 激活Cura环境：`source build/generators/virtual_python_env.sh`
4. 运行Cura：`python cura_app.py`

### 更新依赖
重新运行conan install命令以更新依赖。

## 注意事项

1. **虚拟环境管理**：项目使用两层虚拟环境
   - 第一层：`cura_venv` (用于Conan)
   - 第二层：`build/generators/cura_venv` (用于运行Cura)

2. **并行开发**：修改Uranium或CuraEngine后，需要重新构建相应组件

3. **版本兼容性**：确保三个项目的分支版本兼容

---

搭建完成时间：[将在完成后填入]
搭建状态：[进行中]
